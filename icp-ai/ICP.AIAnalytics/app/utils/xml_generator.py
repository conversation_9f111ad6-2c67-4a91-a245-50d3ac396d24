import xml.etree.ElementTree as ET
from xml.dom import minidom
from typing import Dict, Any, List
from datetime import datetime
import json

from app.models.schemas import ValidationResponse, ValidationResult


class XMLGenerator:
    def __init__(self):
        pass

    def generate_validation_xml(self, validation_data: Dict[str, Any]) -> str:
        """Generate XML output from validation results."""
        try:
            # Create root element
            root = ET.Element("ValidationReport")
            root.set("xmlns", "http://xml-validation-system.com/schema")
            root.set("version", "1.0")
            
            # Add metadata
            metadata = ET.SubElement(root, "Metadata")
            ET.SubElement(metadata, "ValidationId").text = validation_data.get("validation_id", "")
            ET.SubElement(metadata, "Status").text = validation_data.get("status", "")
            ET.SubElement(metadata, "Timestamp").text = str(validation_data.get("validation_timestamp", ""))
            ET.SubElement(metadata, "ProcessingTime").text = str(validation_data.get("processing_time", ""))
            
            # Add file information
            file_info = ET.SubElement(metadata, "FileInformation")
            ET.SubElement(file_info, "QuestionsFileId").text = validation_data.get("questions_file_id", "")
            ET.SubElement(file_info, "ReportFileId").text = validation_data.get("report_file_id", "")
            
            # Add summary
            summary = ET.SubElement(root, "Summary")
            ET.SubElement(summary, "TotalQuestions").text = str(validation_data.get("total_questions", 0))
            ET.SubElement(summary, "ProcessedQuestions").text = str(validation_data.get("processed_questions", 0))
            
            # Calculate summary statistics
            results = validation_data.get("results", [])
            if results:
                confidence_scores = [r.get("confidence_score", 0) for r in results]
                avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
                high_confidence_count = len([s for s in confidence_scores if s >= 0.8])
                
                ET.SubElement(summary, "AverageConfidence").text = f"{avg_confidence:.3f}"
                ET.SubElement(summary, "HighConfidenceAnswers").text = str(high_confidence_count)
            
            # Add validation results
            results_element = ET.SubElement(root, "ValidationResults")
            
            for result in results:
                result_element = ET.SubElement(results_element, "ValidationResult")
                result_element.set("id", result.get("question_id", ""))
                
                # Question information
                question_elem = ET.SubElement(result_element, "Question")
                question_elem.text = result.get("question", "")
                
                # Answer information
                answer_elem = ET.SubElement(result_element, "Answer")
                answer_elem.text = result.get("answer", "")
                answer_elem.set("confidence", str(result.get("confidence_score", 0)))
                answer_elem.set("status", result.get("status", ""))
                
                # Relevant sections
                sections_elem = ET.SubElement(result_element, "RelevantSections")
                for section in result.get("relevant_sections", []):
                    section_elem = ET.SubElement(sections_elem, "Section")
                    section_elem.text = section
            
            # Add validation options if present
            if validation_data.get("validation_options"):
                options_elem = ET.SubElement(root, "ValidationOptions")
                for key, value in validation_data["validation_options"].items():
                    option_elem = ET.SubElement(options_elem, "Option")
                    option_elem.set("name", key)
                    option_elem.text = str(value)
            
            # Convert to pretty XML string
            return self._prettify_xml(root)
            
        except Exception as e:
            raise Exception(f"Error generating XML: {str(e)}")

    def generate_error_xml(self, error_message: str, error_code: str = "VALIDATION_ERROR") -> str:
        """Generate XML for error responses."""
        try:
            root = ET.Element("ErrorResponse")
            root.set("xmlns", "http://xml-validation-system.com/schema")
            
            ET.SubElement(root, "ErrorCode").text = error_code
            ET.SubElement(root, "ErrorMessage").text = error_message
            ET.SubElement(root, "Timestamp").text = datetime.now().isoformat()
            
            return self._prettify_xml(root)
            
        except Exception as e:
            return f"<ErrorResponse><ErrorMessage>Failed to generate error XML: {str(e)}</ErrorMessage></ErrorResponse>"

    def generate_file_upload_xml(self, upload_data: Dict[str, Any]) -> str:
        """Generate XML for file upload responses."""
        try:
            root = ET.Element("FileUploadResponse")
            root.set("xmlns", "http://xml-validation-system.com/schema")
            
            ET.SubElement(root, "FileId").text = upload_data.get("file_id", "")
            ET.SubElement(root, "Filename").text = upload_data.get("filename", "")
            ET.SubElement(root, "FileType").text = upload_data.get("file_type", "")
            ET.SubElement(root, "FileSize").text = str(upload_data.get("file_size", 0))
            ET.SubElement(root, "Status").text = upload_data.get("status", "")
            ET.SubElement(root, "UploadTimestamp").text = str(upload_data.get("upload_timestamp", ""))
            
            # Add questions information if present
            if "total_questions" in upload_data:
                questions_info = ET.SubElement(root, "QuestionsInfo")
                ET.SubElement(questions_info, "TotalQuestions").text = str(upload_data.get("total_questions", 0))
                
                if upload_data.get("questions"):
                    questions_elem = ET.SubElement(questions_info, "Questions")
                    for question in upload_data["questions"]:
                        question_elem = ET.SubElement(questions_elem, "Question")
                        question_elem.set("id", question.get("id", ""))
                        question_elem.text = question.get("question", "")
                        
                        if question.get("category"):
                            question_elem.set("category", question["category"])
                        if question.get("priority"):
                            question_elem.set("priority", question["priority"])
            
            return self._prettify_xml(root)
            
        except Exception as e:
            raise Exception(f"Error generating upload XML: {str(e)}")

    def generate_summary_xml(self, validation_data: Dict[str, Any]) -> str:
        """Generate a summary XML report."""
        try:
            root = ET.Element("ValidationSummary")
            root.set("xmlns", "http://xml-validation-system.com/schema")
            
            # Basic info
            ET.SubElement(root, "ValidationId").text = validation_data.get("validation_id", "")
            ET.SubElement(root, "GeneratedAt").text = datetime.now().isoformat()
            
            # Statistics
            stats = ET.SubElement(root, "Statistics")
            results = validation_data.get("results", [])
            
            ET.SubElement(stats, "TotalQuestions").text = str(len(results))
            
            if results:
                # Calculate statistics
                confidence_scores = [r.get("confidence_score", 0) for r in results]
                answered_count = len([r for r in results if r.get("status") == "answered"])
                error_count = len([r for r in results if r.get("status") == "error"])
                
                ET.SubElement(stats, "AnsweredQuestions").text = str(answered_count)
                ET.SubElement(stats, "ErrorQuestions").text = str(error_count)
                ET.SubElement(stats, "AverageConfidence").text = f"{sum(confidence_scores) / len(confidence_scores):.3f}"
                ET.SubElement(stats, "MinConfidence").text = f"{min(confidence_scores):.3f}"
                ET.SubElement(stats, "MaxConfidence").text = f"{max(confidence_scores):.3f}"
            
            # Top results
            if results:
                top_results = ET.SubElement(root, "TopResults")
                # Sort by confidence score
                sorted_results = sorted(results, key=lambda x: x.get("confidence_score", 0), reverse=True)
                
                for result in sorted_results[:5]:  # Top 5
                    result_elem = ET.SubElement(top_results, "Result")
                    result_elem.set("confidence", str(result.get("confidence_score", 0)))
                    
                    ET.SubElement(result_elem, "Question").text = result.get("question", "")[:100] + "..."
                    ET.SubElement(result_elem, "Answer").text = result.get("answer", "")[:150] + "..."
            
            return self._prettify_xml(root)
            
        except Exception as e:
            raise Exception(f"Error generating summary XML: {str(e)}")

    def _prettify_xml(self, element: ET.Element) -> str:
        """Return a pretty-printed XML string."""
        rough_string = ET.tostring(element, encoding='unicode')
        reparsed = minidom.parseString(rough_string)
        return reparsed.toprettyxml(indent="  ").replace('<?xml version="1.0" ?>\n', '')

    def validate_xml_schema(self, xml_string: str) -> bool:
        """Basic validation of generated XML."""
        try:
            ET.fromstring(xml_string)
            return True
        except ET.ParseError:
            return False 