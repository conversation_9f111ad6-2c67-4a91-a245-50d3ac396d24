{"validation_id": "0a1e37f4-e924-4479-bac8-747be9b73e74", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181489", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "7e457c94-996a-4ed6-8b5d-a2c3632697c2", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "a00c8bb2-651d-4919-bc64-4c6d5d7935d0", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "67d78607-1af1-4d54-bc2f-6fefb8250cdb", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name not present; cannot verify comments or payment section compliance.", "confidence_score": 0.85, "relevant_sections": ["Report/PaymentsSection/CreditOpinion", "Report/FinancialSection/CompanyName"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "114099bd-cffb-4b73-a93a-a8c57c73a849", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name is missing; Payment section has notes, violating compliance.", "confidence_score": 0.9, "relevant_sections": ["Report/PaymentsSection/AdditionalNoteMaxCredit"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "9332fe92-92b9-4fe5-984f-3e243f2e6034", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Gross Profit '80000.00' is less than Total Income '100000.00', compliant.", "confidence_score": 0.98, "relevant_sections": ["FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem[1]", "FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem[2]"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "62095606-5b37-4ddb-bbb4-6e2f5ae748c8", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "No expenses for raw materials found; cannot verify compliance.", "confidence_score": 0.85, "relevant_sections": ["FinancialSection/ProfitAndLoss"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "cebf3f09-ffdc-4fcb-afde-a5cd42a414a1", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Company name 'AishwaryaTest' matches requested name, comments not needed.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ac7fe47c-fd12-4259-9b5e-c6d3f9099091", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address '123 Main St' is active; comments not populated.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressSection/Address", "Report/ClientSpecificComment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8b072b85-ca8f-4a5f-a6db-2922dc95227b", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max Credit Currency is 'AMD', violates EUR/USD/GBP requirement.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/MaxCreditCurrency", "PaymentsSection/MaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2fb6e8ee-0800-4733-9cb8-33bbaacf8cd9", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "No License or Registration numbers found in report sections.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8682fba7-a2e2-475e-a1dc-ae1a16f7a4f7", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "PaymentsSection contains biased language 'Large', violates compliance rules.", "confidence_score": 0.9, "relevant_sections": ["PaymentsSection/CreditOpinion"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "fc90f317-6566-456f-9cec-e9ca4c85bdcb", "question_number": 12, "question": "Spelling Check", "summary": "Spelling error found: 'testt' in SourceDescription of Non Current Assets.", "confidence_score": 0.9, "relevant_sections": ["FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "49a7d481-8eef-4fe0-840b-da09316bc1d9", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No adverse announcements found in significant changes; compliance violated.", "confidence_score": 0.9, "relevant_sections": ["PaymentsSection/CreditOpinion"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "99f7b0ca-079c-4dda-85ae-5cca8c6c1189", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Trade Risk Assessment is 'High', violates expected outcome of NoCredit.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/TradeRiskAssessment", "PaymentsSection/CreditOpinion", "PaymentsSection/MaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "e11542d1-8ca7-4299-baab-4e829c6ce43c", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "Adverse announcements not present; no negative descriptions found.", "confidence_score": 0.9, "relevant_sections": ["Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "101ec1e0-3f3e-44ca-b7f2-d689cb8c8b0a", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks sector-related info; violates compliance rule.", "confidence_score": 0.9, "relevant_sections": ["PaymentsSection/CreditOpinion", "FinancialSection/BalanceSheetFigures/CompanyName"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2256ba9a-40b4-40d6-8edd-da1c138c4abf", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "AdditionalNoteMaxCredit contains 'test', violates export text requirement.", "confidence_score": 0.9, "relevant_sections": ["PaymentsSection/AdditionalNoteMaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2070910c-8f3a-4d60-a077-038298dcb4d6", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income are both absent, cannot verify compliance.", "confidence_score": 0.9, "relevant_sections": ["Financial/ProfitAndLoss/Income", "Financial/ProfitAndLoss/TotalIncome"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "86cd5c68-90f7-474c-af7e-66ed4730acbe", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Trade Risk Assessment is 'Average', violates TRA high requirement.", "confidence_score": 0.95, "relevant_sections": ["PaymentsSection/CreditOpinion", "PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "61b8813b-46aa-47ce-9730-1b7d33413c98", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification 'Average' present, violates non-applicability rule.", "confidence_score": 0.9, "relevant_sections": ["Report/PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "e98dbd67-6ac3-453e-9bac-af431d898d7d", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "TRA classification 'Average' found, violates dormant/ceased trading rule.", "confidence_score": 0.98, "relevant_sections": ["Report/PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "0bce156f-6bd0-405f-a469-cc6d0d1098f1", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number 'Not Listed' violates branch requirement; no special note or transfer selected.", "confidence_score": 0.98, "relevant_sections": ["RelatedEntities/Entity/LicenseNumber", "RelatedEntities/Entity/SpecialNote", "RelatedEntities/Entity/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "b03d9c87-1ce4-4a33-9081-084dc658fc6d", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Comment present: 'Registration number has expired.' but no registration number found.", "confidence_score": 0.85, "relevant_sections": ["Report/Comments/Comment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ece1687a-4e39-4e5c-9d9a-41045322fc30", "question_number": 24, "question": "Small with large amount", "summary": "Max Credit '2321' is less than Credit Figure '2122', violates small with large rule.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/MaxCredit", "PaymentsSection/CreditFigure"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "132c8908-174d-4b54-a172-203ec86fa678", "question_number": 25, "question": "Large with small amount", "summary": "Max Credit '2321' exceeds Credit Figure '2122', violates large with small amount rule", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/CreditOpinion", "PaymentsSection/MaxCredit", "PaymentsSection/CreditFigure"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9494bb5e-5e6a-4b55-ac8c-5778851e8317", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications not present in XML, cannot verify compliance.", "confidence_score": 0.85, "relevant_sections": ["Report/FinancialSection/BalanceSheets"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "6ae737be-9572-4685-8697-8eb8d6ae8c3f", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Header section missing 'Research Type' and required sections not populated.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/PaymentsSection", "Report/FinancialSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "9bed65c6-d707-47cd-9486-be5edd45f71b", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Research Type is 'No Contact', but PaymentHistory is 'No Contact', violates compliance.", "confidence_score": 0.9, "relevant_sections": ["Report/PaymentsSection/PaymentHistory", "Report/HeaderSection/CompanyName"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ae94a997-9dbf-43ba-ae68-9de1b6fc6c75", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Research Type is 'Decline', Comment confirms company declined info.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/Comment"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "7186d153-ae7c-48cc-9309-5b120b685431", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Research Type is blank; Payment comment 'Company has been contacted' is present, Person Interviewed exists.", "confidence_score": 0.9, "relevant_sections": ["Report/PaymentsSection/AdditionalNoteMaxCredit", "Report/PersonInterviewed/Name"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "86207c0b-3bf5-4548-bd0b-4f247a83390e", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanctions search not performed; 'Sanction' section missing in XML.", "confidence_score": 0.98, "relevant_sections": ["Report/PaymentsSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "cb968f99-072f-487e-9019-9c5a952ed65e", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Max Credit 'AMD' present, violates blank requirement; Credit Opinion is empty.", "confidence_score": 0.9, "relevant_sections": ["PaymentsSection/CreditOpinion", "PaymentsSection/MaxCredit", "PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4958f289-9756-4c59-bd2e-219e8f025571", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "46ff298a-125b-45e0-8dfa-8fbc53446596", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in FinancialSection, violates compliance rule.", "confidence_score": 0.98, "relevant_sections": ["FinancialSection/BalanceSheets"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "55857cb9-fa62-4fd2-b844-8c600c4eb214", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "No contact details found; violates requirement for 'Select' research type.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "0d4d5838-dda8-4963-a9af-240da8d56732", "question_number": 36, "question": "Capital missing currency", "summary": "Currency field 'AUD' is present but not capitalized, violates currency rule.", "confidence_score": 0.9, "relevant_sections": ["PaymentsSection/MaxCreditCurrency", "FinancialSection/BalanceSheets/BalanceSheet/Currency"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "24e51a4c-c244-4e71-ba54-56dd503c5af7", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder information is missing, violates related entities rule.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/RelatedEntities/Shareholder"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "ab7eb75d-bc0a-4f99-a5fd-271e3a85ea20", "question_number": 38, "question": "Calculate credit opinion", "summary": "Max Credit '2321' exceeds Credit Figure '2122', violates credit opinion calculation.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/CreditOpinion", "PaymentsSection/MaxCredit", "PaymentsSection/CreditFigure"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "29047457-0f48-472b-b4b7-5e17fa4cf26c", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Company name 'AishwaryaTest' matches header name 'AishwaryaTest'", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/FinancialSection/BalanceSheetFigures/CompanyName"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "3257a803-dd75-4b26-bb0e-fa7bde4a8213", "question_number": 40, "question": "Town missing in town box", "summary": "Town box is empty, violates requirement for address completeness.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/PaymentsSection", "Report/FinancialSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "0fc3dae9-1275-485d-972a-2ddac1baca56", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status not provided; credit opinion 'Large' violates compliance rule.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/CreditOpinion", "LegalSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-15 19:52:49.585917", "processing_time": 80.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "0447", "rag_enabled": true, "permanent_question_bank_used": true}