{"validation_id": "7383b162-1ac8-493b-b54f-015e67dd4daa", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181493", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "bcf78954-08d2-4e44-ae38-169175edb691", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question for 'TURKEXIM' but report for 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "794eb6e7-b360-498b-9a6b-3ed7e1a0068a", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question for '??' but report for 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "efa51791-366d-48f8-a94a-b613a4be4229", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Review needed: The XML data provided does not contain any information regarding a 'Client Name' field or any corresponding details in the 'Client S...", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c0896816-004d-4c44-9eda-f95dba6d1475", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "? The XML data does not contain a 'Client Name' field in the provided sections.", "confidence_score": 0.5, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "7ba5a2e5-ff75-4b18-85d5-388f8a14977d", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Review needed: The XML data provided does not include any information regarding Gross Profit or Total Income.", "confidence_score": 0.0, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ea0b5d68-3bfa-4b23-b22c-2b5c47a948d6", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Review needed: The provided XML data does not contain any information regarding the financial info section, specifically the Expenses for raw mater...", "confidence_score": 0.5, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ada4e25f-9d3a-4b8e-a2a5-9c3b5e117d56", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "✓ The Company Name provided in the HeaderSection is 'GenAI25', which matches the Requested Name also listed as 'GenAI25'.", "confidence_score": 1.0, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "4693346c-9c9e-4f11-b067-2ba687934de2", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "✗ The address provided by the client does not match any active address in the Address section, as indicated by the ActiveAddress content being 'fal...", "confidence_score": 0.95, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ActiveAddress", "Report/AddressesSection/Addresses/Address/BuildingOwner", "Report/AddressesSection/Addresses/Address/Street", "Report/AddressesSection/Addresses/Address/Area", "Report/AddressesSection/Addresses/Address/POBox", "Report/AddressesSection/Addresses/Address/ISO2CountryCode", "Report/AddressesSection/Addresses/Address/ICPCountryCode", "Report/AddressesSection/Addresses/Address/ICPCountryName"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "a26443ef-1b86-4f52-b90c-e4ae4cb67fba", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "✗ The XML data indicates that the 'TransferToPrincipal' under 'SpecialNotesSection' is set to 'false'.", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "e971197f-5e10-4373-8392-8fbfa30bd4d6", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "Approved: The XML data does not contain any registration or license numbers in the Report/LegalStatusSection or Report/SpecialNotesSection.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "13caf67c-fb6d-45d8-a572-5a8ca5f5a439", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "Review needed: The XML data contains terms such as 'test' in multiple fields (BuildingOwner, Street, Area, etc.), which are vague and could be cons...", "confidence_score": 0.85, "relevant_sections": ["Report/AddressesSection/Addresses/Address/BuildingOwner", "Report/AddressesSection/Addresses/Address/Street", "Report/AddressesSection/Addresses/Address/Area", "Report/WaiverSection/Waiver"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "e129f652-1e00-4d3e-bf69-0a7653feecf1", "question_number": 12, "question": "Spelling Check", "summary": "Rejected: The XML data contains multiple instances of the word 'test' in the Address section, which is not a proper name or term and may indicate a...", "confidence_score": 0.85, "relevant_sections": ["Report/AddressesSection/Addresses/Address/BuildingOwner", "Report/AddressesSection/Addresses/Address/Street", "Report/AddressesSection/Addresses/Address/Area", "Report/LegalStatusSection/LegalStatusAdditional", "Report/LegalStatusSection/ParentCompany", "Report/LegalStatusSection/Subsidiaries"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "86a114e9-7e6f-4e43-878f-61d727b7054c", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "Rejected: The XML data provided does not contain any mention of adverse announcements or significant changes.", "confidence_score": 0.95, "relevant_sections": ["Report/WaiverSection/Waiver"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "1a658ab2-7603-44c1-ba4e-e21a9e3147f5", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "? The provided XML data does not contain any information regarding adverse announcements, significant changes, payments sections, trade risk assess...", "confidence_score": 0.85, "relevant_sections": ["Report/WaiverSection/Waiver"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "7ee502c0-32e0-4c5d-94c9-f8854b11cd0b", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "Rejected: The XML data does not contain any adverse announcements or descriptions.", "confidence_score": 0.95, "relevant_sections": [], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "9f12532c-11ba-4d25-a102-e6d37f772640", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Rejected: The XML data does not contain any information in the Payments section, nor does it provide any relevant details about the sector that the...", "confidence_score": 0.95, "relevant_sections": [], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "68ca3b58-ce54-4cc5-95e2-2367e066d9ac", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Review needed: The XML data does not contain any specific service activities or texts related to export that can be validated against the rule.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9b1c8636-8d72-41e9-96f6-6d0999882df9", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Review needed: The XML data does not contain any elements for 'Income' or 'Total Income'.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d8fe3c8a-2df1-4e72-8b1c-f6fee621ba97", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Rejected: The validation rule states that a credit opinion in a different currency is requested, no credit is given, and the TRA is high.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/Capital/NotGiven"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "17d4ceed-658d-4be3-bc65-a270937428b0", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Approved: The XML data indicates that the ISO2CountryCode is 'IN', the ICPCountryCode is 'IND', and the ICPCountryName is 'India'.", "confidence_score": 0.95, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ISO2CountryCode", "Report/AddressesSection/Addresses/Address/ICPCountryCode", "Report/AddressesSection/Addresses/Address/ICPCountryName"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "b801a03a-e980-4087-8627-ae96fd8c65dd", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Approved: The XML data indicates that the company status is 'Active' as per the path Report/LegalStatusSection/CompanyStatus.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "200b562d-f318-48da-9489-1972c3beca5f", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "✗ The validation rule states that the license number must be for a Registered branch and must be listed within the Related entities section as a Br...", "confidence_score": 0.9, "relevant_sections": [], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b833fb68-dc01-4206-887e-ab617278ce15", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Rejected: The XML data does not provide any information regarding registration numbers or their expiry status.", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "58d0aa0c-a9bd-4e4b-bb21-e1f69303ac1f", "question_number": 24, "question": "Small with large amount", "summary": "✗ The validation rule 'Small with large amount' suggests that there should be a small amount of information provided in the XML data, but the conte...", "confidence_score": 0.95, "relevant_sections": ["Report/WaiverSection/Waiver"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "0c70b3f8-f962-4de7-a586-aeba83754117", "question_number": 25, "question": "Large with small amount", "summary": "Rejected: The validation rule 'Large with small amount' suggests that there should be a significant amount of information provided in a concise man...", "confidence_score": 0.95, "relevant_sections": ["Report/WaiverSection/Waiver"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6d2b395c-b1bf-4261-adc8-68f09332ed4e", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Review needed: The XML data does not contain any information regarding position classifications, which is necessary to determine if they are simila...", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "fd90ae16-ec4e-40e7-8ad4-b60f0f071ac5", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Rejected: The XML data does not comply with the validation rule because it lacks the required indication that the research type is 'Negative'.", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/HeaderSection/CompanyName", "Report/AddressesSection/Addresses/Address"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "bf9ba066-5174-4fcf-a283-8f45a8b82644", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Rejected: The XML data does not provide any indication that the research type is 'No Contact'.", "confidence_score": 0.9, "relevant_sections": [], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "f16d7b7c-8bdd-4a8f-a123-300c272075db", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Rejected: The XML data does not contain any information indicating that the research type is 'Decline' or that the subject company has been contact...", "confidence_score": 0.95, "relevant_sections": [], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "c58b4512-7208-49e1-aafb-ae3f0478ea2a", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Rejected: The XML data does not contain any information regarding the research type, payments section, or the Person Interviewed.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/LegalStatusSection/CompanyStatus", "Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/ParentCompany", "Report/WaiverSection/Waiver", "Report/AddressesSection/Addresses/Address/ICPCountryName", "Report/LegalStatusSection/DateStarted", "Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/Capital/NotGiven", "Report/LegalStatusSection/LegalStatusCode", "Report/LegalStatusSection/LegalStatus", "Report/LegalStatusSection/LegalStatusAdditional", "Report/LegalStatusSection/Subsidiaries", "Report/HeaderSection/Date", "Report/HeaderSection/DeliveryDate"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "cd77d409-cd55-4d23-bb21-c03277657524", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "✗ The XML data does not contain any indication that a sanctions search was performed.", "confidence_score": 0.98, "relevant_sections": [], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "d090af01-c9ff-43a3-b0a0-d5776c9d448e", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Review needed: The provided XML data does not contain any information regarding sanctions, the trade risk assessment, credit opinion, max credit, o...", "confidence_score": 0.85, "relevant_sections": ["Report/WaiverSection/Waiver"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "43df01f2-37e3-4f0a-b8f2-177afffc9efd", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question for 'TURKEXIM' but report for 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "dddc5825-4673-4fed-8710-5fe340951e19", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Rejected: The XML data does not include any mention of a provenance icon in the relevant sections.", "confidence_score": 0.95, "relevant_sections": ["Report/WaiverSection/Waiver"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "c32f6bb3-470b-4ef9-940b-6cd8302eaec4", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Review needed: The XML data does not contain any telephone number or email address in the provided sections.", "confidence_score": 0.85, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ActiveAddress", "Report/AddressesSection/Addresses/Address/BuildingOwner", "Report/AddressesSection/Addresses/Address/Street", "Report/AddressesSection/Addresses/Address/Area", "Report/AddressesSection/Addresses/Address/POBox", "Report/AddressesSection/Addresses/Address/ISO2CountryCode", "Report/AddressesSection/Addresses/Address/ICPCountryCode", "Report/AddressesSection/Addresses/Address/ICPCountryName", "Report/WaiverSection/Waiver", "Report/HeaderSection/Date", "Report/HeaderSection/DeliveryDate", "Report/HeaderSection/OurRef", "Report/HeaderSection/YourRef", "Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "eb258a5c-bbcb-4f1b-acc4-f9b4816445d1", "question_number": 36, "question": "Capital missing currency", "summary": "✗ The validation rule states that a currency must be specified for the capital amount.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/Capital/NotGiven"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "9dcc41ac-d03b-48de-895d-633a039be6a3", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Rejected: The XML data does not contain any information regarding a Shareholder.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b80b372d-a3fd-427b-a533-70b4ae2c36f4", "question_number": 38, "question": "Calculate credit opinion", "summary": "Rejected: The XML data does not provide any specific information regarding the credit opinion calculation, such as credit scores, financial ratios,...", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital", "Report/LegalStatusSection/CompanyStatus"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6c6110b3-3295-46dc-a345-c8dcc5581d36", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "✗ The CompanyName in the header section is 'GenAI25', while the Requested field in the header section also contains 'GenAI25'.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "ea5a78fa-c046-47c6-beec-6ea2648aa9b4", "question_number": 40, "question": "Town missing in town box", "summary": "✗ The XML data does not contain a 'Town' element within the 'Address' section.", "confidence_score": 0.95, "relevant_sections": ["Report/AddressesSection/Addresses/Address"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "7843c9ce-1335-46b8-a7fb-afaaf6e9d8ca", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Approved: The Company status in the legal section is 'Active', which is a known status.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "approved", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-11 15:31:28.001691", "processing_time": 25.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "ONLINEMISC", "rag_enabled": true, "permanent_question_bank_used": true}