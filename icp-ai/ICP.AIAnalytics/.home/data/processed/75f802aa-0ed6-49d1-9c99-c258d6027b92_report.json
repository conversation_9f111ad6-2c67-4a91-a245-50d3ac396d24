{"file_id": "75f802aa-0ed6-49d1-9c99-c258d6027b92", "xml_structure": {"Report": {"@xmlns:xsd": "http://www.w3.org/2001/XMLSchema", "@xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "HeaderSection": {"Date": "30-May-2025", "DeliveryDate": "30-May-2025", "OurRef": "999517.45.1", "YourRef": "test", "CompanyName": "RE test 2", "Requested": "test"}, "AddressesSection": {"Addresses": {"Address": {"ActiveAddress": "true", "CareOf": "test", "Building": "test", "BuildingOwner": "test", "Street": "test", "Area": "test", "POBox": "test", "Town": "test", "ProvinceType": "Province", "ISO2CountryCode": "AF", "ICPCountryCode": "AFG", "ICPCountryName": "Afghanistan"}}}, "LegalStatusSection": {"CompanyStatus": "Active", "RegistrationNumbers": {"RegistrationNumber": {"ICPRegistrationNumberName": "Other Registration Number1", "LocalRegistrationNumberName": "qq", "LocalAbbreviation": "qq", "IssuingAuthority": "qq", "RegistrationNumberValue": "123", "DateIssued": "20-Nov-2024", "DateExpired": "27-Nov-2024", "RenewalFrequency": "Bi Annually", "ICPRegistrationNumberTypeId": "1"}}, "Capital": {"NotGiven": "false"}, "ICPLegalGroup": "Branch/Office of a Foreign Registered Company", "LegalStatusCode": "5500", "LegalStatus": "Branch of a Foreign Registered Company", "Shareholders": {"Shareholder": [{"Name": "atest", "NoOfShares": "0", "Shareholding": "0.00"}, {"Name": "test 2", "Nationality": "Angolan", "NoOfShares": "60", "Shareholding": "60.00"}, {"Name": "test 1", "Nationality": "Azerbaijani", "NoOfShares": "10", "Shareholding": "30.00"}, {"Name": "test 3", "Nationality": "Austrian", "NoOfShares": "40", "Shareholding": "40.00"}]}}, "RelatedEntitiesSection": {"RelatedEntity": [{"Relationship": "Parent", "CompanyName": "Angular UAT Testing", "CSRNumber": "998279", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "DateOccupiedFrom": "30-Nov-2023", "DateOccupiedTo": "23-Nov-2030", "CareOf": "Test0.3", "Building": "Test0.3", "BuildingCommercialName": "Test0.3", "BuildingOwner": "Test0.3", "Street": "Test0.3", "Area": "Test0.3", "POBox": "Test0.3", "Town": "Test0.3", "PostCode": "1234", "ISO2CountryCode": "PG", "ICPCountryCode": "ADM", "ICPCountryName": "Admiralty Islands", "GPSLocation": "tudip", "OccupancyType": "Own"}, "Telephone": {"InternationalDialingCode": "213", "Number": "3333333", "Extension": "11", "Type": "Fax"}}, "RelatedEntitiesTradingStyles": {"TradingStyle": [{"Type": "Short Form", "EnglishName": "3433Test...33"}, {"Type": "Trading Style", "EnglishName": "Test0.3"}, {"Type": "Trading Style", "EnglishName": "Tudip0.3"}]}, "RelatedEntitiesLegalStatusSection": {"DateStarted": "11-Apr-2020", "CompanyStatus": "Active", "History": "Test0.3", "RegistrationNumbers": {"RegistrationNumber": [{"ICPRegistrationNumberName": "Other Registration Number", "LocalRegistrationNumberName": "<PERSON> Test2", "LocalAbbreviation": "AT2", "IssuingAuthority": "456789", "RegistrationNumberValue": "32433333", "DateIssued": "07-Mar-2024", "DateExpired": "29-Mar-2024", "RenewalFrequency": "Annually", "Comments": "test", "ICPRegistrationNumberTypeId": "-1"}, {"ICPRegistrationNumberName": "Other Registration Number2", "LocalRegistrationNumberName": "<PERSON> Test1", "LocalAbbreviation": "AT1", "RegistrationNumberValue": "345678", "DateIssued": "18-Mar-2024", "DateExpired": "29-Mar-2024", "RenewalFrequency": "Annually", "ICPRegistrationNumberTypeId": "2"}, {"ICPRegistrationNumberName": "Other Registration Number1", "LocalRegistrationNumberName": "Licence No.", "LocalAbbreviation": "BLN abb", "IssuingAuthority": "Afghanistan Central Business Registry", "RegistrationNumberValue": "3456789", "DateIssued": "06-Mar-2024", "DateExpired": "28-Mar-2024", "RenewalFrequency": "Annually", "ICPRegistrationNumberTypeId": "1"}]}, "ICPLegalGroup": "Government", "LegalStatusCode": "2088", "LegalStatus": "Government Agency", "LegalStatusAdditional": "Test0.3"}, "RelatedEntitiesFinancialSection": {"BalanceSheetFigures": {"AccountType": "Consolidated (Subject & Subsidiaries)", "CompanyName": "Angular UAT Testing", "ScaleTheme": "Units"}, "BalanceSheets": {"BalanceSheet": {"FiscalYear": "2012", "FiscalYearEndDate": "05 July", "InformationType": "Extract", "InformationLevel": "Projected", "FinancialPeriodEndDate": "05-Jul-2012", "NumberOfMonths": "5", "Currency": "ANG", "Scale": "Units", "LineItems": {"LineItem": [{"Level1": "Assets", "Level2": "Current Assets", "Level3": "Tax Asset", "Level4": "Line Item", "SourceDescription": "test2", "Amount": "69.00", "CategoryId": "2", "LineItemTypeId": "1"}, {"Level1": "Assets", "Level2": "Non Current Assets", "Level3": "Deferred Tax Asset", "Level4": "Line Item", "SourceDescription": "test", "Amount": "20.00", "CategoryId": "1", "LineItemTypeId": "1"}]}}}, "ProfitAndLossFigures": {"AccountType": "Consolidated (Subject & Subsidiaries)", "CompanyName": "Angular UAT Testing", "ScaleTheme": "Units"}, "ProfitAndLossAccts": {"ProfitAndLossAcct": {"FiscalYear": "2023", "FiscalYearEndDate": "05 May", "InformationType": "Extract", "InformationLevel": "Partial", "FinancialPeriodEndDate": "05-May-2023", "NumberOfMonths": "4", "Currency": "AMD", "Scale": "Units", "LineItems": {"LineItem": [{"Level1": "Gross Profit", "Level2": "Gross Profit", "Level3": "Gross Profit", "Level4": "Subtotal", "SourceDescription": "Gross Profit", "Amount": "3000.00", "KeyFinancialId": "11", "LineItemTypeId": "2"}, {"Level1": "Operating Expenses", "Level2": "Total Operating Expenses", "Level3": "Total Operating Expenses", "Level4": "Subtotal", "SourceDescription": "Total Operating Expenses", "Amount": "3000.00", "KeyFinancialId": "12", "LineItemTypeId": "2"}, {"Level1": "Adjusted P&L", "Level2": "Total Adjusted P&L", "Level3": "Total Adjusted Profit", "Level4": "Total", "SourceDescription": "Total Adjusted Profit", "Amount": "2332000.00", "KeyFinancialId": "16", "LineItemTypeId": "3"}, {"Level1": "Operating Profit", "Level2": "Total Operating Profit", "Level3": "Total Operating Profit", "Level4": "Subtotal", "SourceDescription": "Total Operating Profit", "Amount": "3000.00", "KeyFinancialId": "13", "LineItemTypeId": "2"}, {"Level1": "Net P&L", "Level2": "Total Profit Before Tax", "Level3": "Profit Before Tax", "Level4": "Subtotal", "SourceDescription": "Profit Before Tax", "Amount": "2324000.00", "KeyFinancialId": "14", "LineItemTypeId": "2"}, {"Level1": "Profit After Tax", "Level2": "Net Profit", "Level3": "Taxation", "Level4": "Line Item", "SourceDescription": "red3", "Amount": "3000.00", "CategoryId": "11", "LineItemTypeId": "1"}, {"Level1": "Operating Expenses", "Level2": "Operating Expenses", "Level3": "Operating Non-Recurring", "Level4": "Line Item", "SourceDescription": "ewrf", "Amount": "3000.00", "CategoryId": "8", "LineItemTypeId": "1"}, {"Level1": "Profit After Tax", "Level2": "Total Net Profit", "Level3": "Net Profit", "Level4": "Subtotal", "SourceDescription": "Net Profit", "Amount": "23000.00", "KeyFinancialId": "15", "LineItemTypeId": "2"}, {"Level1": "Net P&L", "Level2": "Profit Before Tax", "Level3": "Finance Costs", "Level4": "Line Item", "SourceDescription": "wefwe", "Amount": "232000.00", "CategoryId": "10", "LineItemTypeId": "1"}, {"Level1": "Direct Costs", "Level2": "Direct Costs", "Level3": "Cost of Sales", "Level4": "Line Item", "SourceDescription": "df", "Amount": "4000.00", "CategoryId": "7", "LineItemTypeId": "1"}, {"Level1": "Income", "Level2": "Income", "Level3": "Income Other", "Level4": "Line Item", "SourceDescription": "sd", "Amount": "45000.00", "CategoryId": "6", "LineItemTypeId": "1"}, {"Level1": "Direct Costs", "Level2": "Total Direct Costs", "Level3": "Total Direct Costs", "Level4": "Subtotal", "SourceDescription": "Total Direct Costs", "Amount": "4000.00", "KeyFinancialId": "10", "LineItemTypeId": "2"}]}}}, "AdditionalInfo": "null\n\nhh"}}, {"Relationship": "Affiliate", "CompanyName": "Lite Report Test RE", "CSRNumber": "999516", "RelatedEntitiesLegalStatusSection": {"CompanyStatus": "Active", "ICPLegalGroup": "Civil Company/Partnership - Non-Commercial", "LegalStatusCode": "1011", "LegalStatus": "Civil Company"}}, {"Relationship": "Branch", "CompanyName": "test 11", "CSRNumber": "999768", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "AO", "ICPCountryCode": "ANGO", "ICPCountryName": "Angola"}}}, {"Relationship": "Branch", "CompanyName": "test 11", "CSRNumber": "999769", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "AO", "ICPCountryCode": "ANGO", "ICPCountryName": "Angola"}}}, {"Relationship": "Associate", "CompanyName": "test 12", "CSRNumber": "999770", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "DZ", "ICPCountryCode": "ALG", "ICPCountryName": "Algeria"}}}, {"Relationship": "Branch", "CompanyName": "test 13", "CSRNumber": "999771", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AI", "ICPCountryCode": "ANGU", "ICPCountryName": "<PERSON><PERSON><PERSON>"}}}, {"Relationship": "Branch", "CompanyName": "test 16", "CSRNumber": "999774", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "DZ", "ICPCountryCode": "ALG", "ICPCountryName": "Algeria"}}}, {"Relationship": "Affiliate", "CompanyName": "test 19", "CSRNumber": "999777", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "DZ", "ICPCountryCode": "ALG", "ICPCountryName": "Algeria"}}}, {"Relationship": "Subsidiary", "CompanyName": "test 20", "CSRNumber": "999778", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Region", "ISO2CountryCode": "AL", "ICPCountryCode": "ALB", "ICPCountryName": "Albania"}}}, {"Relationship": "Subsidiary", "CompanyName": "test 21", "CSRNumber": "999779", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Affiliate", "CompanyName": "test 31", "CSRNumber": "999785", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Region", "ISO2CountryCode": "AD", "ICPCountryCode": "AND", "ICPCountryName": "Andorra"}}}, {"Relationship": "Associate", "CompanyName": "test 345", "CSRNumber": "999789", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "AO", "ICPCountryCode": "ANGO", "ICPCountryName": "Angola"}}}, {"Relationship": "Associate", "CompanyName": "test 345", "CSRNumber": "999790", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "AO", "ICPCountryCode": "ANGO", "ICPCountryName": "Angola"}}}, {"Relationship": "Branch", "CompanyName": "test 35", "CSRNumber": "999791", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AI", "ICPCountryCode": "ANGU", "ICPCountryName": "<PERSON><PERSON><PERSON>"}}}, {"Relationship": "Branch", "CompanyName": "test 35", "CSRNumber": "999792", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AI", "ICPCountryCode": "ANGU", "ICPCountryName": "<PERSON><PERSON><PERSON>"}}}, {"Relationship": "Affiliate", "CompanyName": "test 37", "CSRNumber": "999796", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Region", "ISO2CountryCode": "AL", "ICPCountryCode": "ALB", "ICPCountryName": "Albania"}}}, {"Relationship": "Branch", "CompanyName": "test 388", "CSRNumber": "999797", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Region", "ISO2CountryCode": "AD", "ICPCountryCode": "AND", "ICPCountryName": "Andorra"}}}, {"Relationship": "Affiliate", "CompanyName": "test 390", "CSRNumber": "999811", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Region", "ISO2CountryCode": "AL", "ICPCountryCode": "ALB", "ICPCountryName": "Albania"}}}, {"Relationship": "Subsidiary", "CompanyName": "test 40", "CSRNumber": "999812", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Region", "ISO2CountryCode": "AD", "ICPCountryCode": "AND", "ICPCountryName": "Andorra"}}}, {"Relationship": "Subsidiary", "CompanyName": "test 42", "CSRNumber": "999840", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "DateOccupiedFrom": "06-Jan-2025", "DateOccupiedTo": "06-Jan-2025", "ProvinceType": "Province", "ISO2CountryCode": "AO", "ICPCountryCode": "ANGO", "ICPCountryName": "Angola"}}}, {"Relationship": "Affiliate", "CompanyName": "test 43", "CSRNumber": "999842", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "DateOccupiedFrom": "08-Jan-2025", "DateOccupiedTo": "10-Jan-2025", "CareOf": "test", "Building": "test", "BuildingCommercialName": "test", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Associate", "CompanyName": "test 7", "CSRNumber": "999763", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "AO", "ICPCountryCode": "ANGO", "ICPCountryName": "Angola"}}}, {"Relationship": "Affiliate", "CompanyName": "test 8", "CSRNumber": "999764", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Affiliate", "CompanyName": "test 8", "CSRNumber": "999765", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Affiliate", "CompanyName": "test 8", "CSRNumber": "999766", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Associate", "CompanyName": "test14", "CSRNumber": "999772", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Region", "ISO2CountryCode": "AL", "ICPCountryCode": "ALB", "ICPCountryName": "Albania"}}}, {"Relationship": "Associate", "CompanyName": "test15", "CSRNumber": "999773", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AI", "ICPCountryCode": "ANGU", "ICPCountryName": "<PERSON><PERSON><PERSON>"}}}, {"Relationship": "Associate", "CompanyName": "test28", "CSRNumber": "999782", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Associate", "CompanyName": "test299", "CSRNumber": "999783", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Region", "ISO2CountryCode": "AD", "ICPCountryCode": "AND", "ICPCountryName": "Andorra"}}}, {"Relationship": "Associate", "CompanyName": "test30", "CSRNumber": "999784", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "AO", "ICPCountryCode": "ANGO", "ICPCountryName": "Angola"}}}, {"Relationship": "Shareholder", "CompanyName": "test322", "CSRNumber": "999786", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Shareholder", "CompanyName": "test322", "CSRNumber": "999787", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Shareholder", "CompanyName": "test322", "CSRNumber": "999788", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Branch", "CompanyName": "test36", "CSRNumber": "999793", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Branch", "CompanyName": "test36", "CSRNumber": "999794", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Branch", "CompanyName": "test36", "CSRNumber": "999795", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Subsidiary", "CompanyName": "TestForDeleteVersion0.2RE", "CSRNumber": "999300", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "DateOccupiedFrom": "13-Aug-2024", "DateOccupiedTo": "24-Aug-2024", "CareOf": "Test0th", "Building": "Test0th", "BuildingCommercialName": "Test0th", "BuildingOwner": "Test0th", "Street": "Test0th", "Area": "Test0th", "POBox": "Test0th", "Town": "Test0th", "ISO2CountryCode": "IN", "ICPCountryCode": "IND", "ICPCountryName": "India"}, "Telephone": {"Number": "4353535"}}, "RelatedEntitiesTradingStyles": {"TradingStyle": {"Type": "Formerly Known", "EnglishName": "*********"}}, "RelatedEntitiesLegalStatusSection": {"DateStarted": "13-Mar-2023", "CompanyStatus": "Active", "History": "Test12", "RegistrationNumbers": {"RegistrationNumber": {"ICPRegistrationNumberName": "Business Registration Number", "LocalRegistrationNumberName": "test", "RegistrationNumberValue": "123221", "ICPRegistrationNumberTypeId": "4"}}, "ICPLegalGroup": "Institution", "LegalStatusCode": "7601", "LegalStatus": "Financial Institution", "LegalStatusAdditional": "Test"}}, {"Relationship": "Affiliate", "CompanyName": "testing 17", "CSRNumber": "999775", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AI", "ICPCountryCode": "ANGU", "ICPCountryName": "<PERSON><PERSON><PERSON>"}}}, {"Relationship": "Shareholder", "CompanyName": "testing 188", "CSRNumber": "999776", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "AO", "ICPCountryCode": "ANGO", "ICPCountryName": "Angola"}}}, {"Relationship": "Associate", "CompanyName": "testing 2", "CSRNumber": "999751", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Associate", "CompanyName": "testing 2", "CSRNumber": "999752", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Associate", "CompanyName": "testing 2", "CSRNumber": "999753", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Affiliate", "CompanyName": "testing 5", "CSRNumber": "999758", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "DZ", "ICPCountryCode": "ALG", "ICPCountryName": "Algeria"}}}, {"Relationship": "Affiliate", "CompanyName": "testing 5", "CSRNumber": "999759", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "DZ", "ICPCountryCode": "ALG", "ICPCountryName": "Algeria"}}}, {"Relationship": "Affiliate", "CompanyName": "testing 5", "CSRNumber": "999760", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "DZ", "ICPCountryCode": "ALG", "ICPCountryName": "Algeria"}}}, {"Relationship": "Affiliate", "CompanyName": "testing 5", "CSRNumber": "999761", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "DZ", "ICPCountryCode": "ALG", "ICPCountryName": "Algeria"}}}, {"Relationship": "Affiliate", "CompanyName": "testing 6", "CSRNumber": "999762", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Region", "ISO2CountryCode": "AL", "ICPCountryCode": "ALB", "ICPCountryName": "Albania"}}}, {"Relationship": "Affiliate", "CompanyName": "testing 9", "CSRNumber": "999767", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Region", "ISO2CountryCode": "AL", "ICPCountryCode": "ALB", "ICPCountryName": "Albania"}}}, {"Relationship": "Subsidiary", "CompanyName": "testing duplicate 1", "CSRNumber": "999839", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "AO", "ICPCountryCode": "ANGO", "ICPCountryName": "Angola"}}}, {"Relationship": "Affiliate", "CompanyName": "testing1", "CSRNumber": "999750", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "DateOccupiedFrom": "01-Nov-2024", "DateOccupiedTo": "02-Nov-2024", "ISO2CountryCode": "AI", "ICPCountryCode": "ANGU", "ICPCountryName": "<PERSON><PERSON><PERSON>"}}}, {"Relationship": "Branch", "CompanyName": "testing3", "CSRNumber": "999754", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AI", "ICPCountryCode": "ANGU", "ICPCountryName": "<PERSON><PERSON><PERSON>"}}}, {"Relationship": "Branch", "CompanyName": "testing3", "CSRNumber": "999755", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AI", "ICPCountryCode": "ANGU", "ICPCountryName": "<PERSON><PERSON><PERSON>"}}}, {"Relationship": "Branch", "CompanyName": "testing3", "CSRNumber": "999756", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AI", "ICPCountryCode": "ANGU", "ICPCountryName": "<PERSON><PERSON><PERSON>"}}}, {"Relationship": "Branch", "CompanyName": "testing4", "CSRNumber": "999757", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}, {"Relationship": "Associate", "CompanyName": "testt 24", "CSRNumber": "999780", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ProvinceType": "Province", "ISO2CountryCode": "AO", "ICPCountryCode": "ANGO", "ICPCountryName": "Angola"}}}, {"Relationship": "Associate", "CompanyName": "testt 25", "CSRNumber": "999781", "RelatedEntitiesAddressSection": {"Address": {"ActiveAddress": "true", "ISO2CountryCode": "AG", "ICPCountryCode": "ANT", "ICPCountryName": "Antigua"}}}]}, "SpecialNotesSection": {"TransferToPrincipal": "false"}, "WaiverSection": {"Waiver": "All information within this report is provided entirely on an 'as is' basis and is used by the recipient at its sole risk. ICP shall use reasonable skill and care in providing information contained within this report and, specifically, in collecting and collating the information from reliable sources and will have no legal responsibility for any error or omission contained within this report or subsequent change to the information. It does not give any warranty or guarantee to information provided or consequences of using the information within this report for decision making purposes.\n\nInformation is to be stored and used in accordance with data protection regulation and will not be supplied, published, displayed, re-sold, copied or made known to the subject without prior agreement from ICP."}}}, "text_content": [{"path": "Report/HeaderSection/Date", "tag": "Date", "text": "30-May-2025", "attributes": {}}, {"path": "Report/HeaderSection/DeliveryDate", "tag": "DeliveryDate", "text": "30-May-2025", "attributes": {}}, {"path": "Report/HeaderSection/OurRef", "tag": "OurRef", "text": "999517.45.1", "attributes": {}}, {"path": "Report/HeaderSection/YourRef", "tag": "YourRef", "text": "test", "attributes": {}}, {"path": "Report/HeaderSection/CompanyName", "tag": "CompanyName", "text": "RE test 2", "attributes": {}}, {"path": "Report/HeaderSection/Requested", "tag": "Requested", "text": "test", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/CareOf", "tag": "CareOf", "text": "test", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/Building", "tag": "Building", "text": "test", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/BuildingOwner", "tag": "BuildingOwner", "text": "test", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/Street", "tag": "Street", "text": "test", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/Area", "tag": "Area", "text": "test", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/POBox", "tag": "POBox", "text": "test", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/Town", "tag": "Town", "text": "test", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AF", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "AFG", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Afghanistan", "attributes": {}}, {"path": "Report/LegalStatusSection/CompanyStatus", "tag": "CompanyStatus", "text": "Active", "attributes": {}}, {"path": "Report/LegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberName", "tag": "ICPRegistrationNumberName", "text": "Other Registration Number1", "attributes": {}}, {"path": "Report/LegalStatusSection/RegistrationNumbers/RegistrationNumber/LocalRegistrationNumberName", "tag": "LocalRegistrationNumberName", "text": "qq", "attributes": {}}, {"path": "Report/LegalStatusSection/RegistrationNumbers/RegistrationNumber/LocalAbbreviation", "tag": "LocalAbbreviation", "text": "qq", "attributes": {}}, {"path": "Report/LegalStatusSection/RegistrationNumbers/RegistrationNumber/IssuingAuthority", "tag": "IssuingAuthority", "text": "qq", "attributes": {}}, {"path": "Report/LegalStatusSection/RegistrationNumbers/RegistrationNumber/RegistrationNumberValue", "tag": "RegistrationNumberValue", "text": "123", "attributes": {}}, {"path": "Report/LegalStatusSection/RegistrationNumbers/RegistrationNumber/DateIssued", "tag": "DateIssued", "text": "20-Nov-2024", "attributes": {}}, {"path": "Report/LegalStatusSection/RegistrationNumbers/RegistrationNumber/DateExpired", "tag": "DateExpired", "text": "27-Nov-2024", "attributes": {}}, {"path": "Report/LegalStatusSection/RegistrationNumbers/RegistrationNumber/RenewalFrequency", "tag": "RenewalFrequency", "text": "Bi Annually", "attributes": {}}, {"path": "Report/LegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberTypeId", "tag": "ICPRegistrationNumberTypeId", "text": "1", "attributes": {}}, {"path": "Report/LegalStatusSection/Capital/NotGiven", "tag": "NotGiven", "text": "false", "attributes": {}}, {"path": "Report/LegalStatusSection/ICPLegalGroup", "tag": "ICPLegalGroup", "text": "Branch/Office of a Foreign Registered Company", "attributes": {}}, {"path": "Report/LegalStatusSection/LegalStatusCode", "tag": "LegalStatusCode", "text": "5500", "attributes": {}}, {"path": "Report/LegalStatusSection/LegalStatus", "tag": "LegalStatus", "text": "Branch of a Foreign Registered Company", "attributes": {}}, {"path": "Report/LegalStatusSection/Shareholders/Shareholder/Name", "tag": "Name", "text": "atest", "attributes": {}}, {"path": "Report/LegalStatusSection/Shareholders/Shareholder/NoOfShares", "tag": "NoOfShares", "text": "0", "attributes": {}}, {"path": "Report/LegalStatusSection/Shareholders/Shareholder/Shareholding", "tag": "Shareholding", "text": "0.00", "attributes": {}}, {"path": "Report/LegalStatusSection/Shareholders/Shareholder/Name", "tag": "Name", "text": "test 2", "attributes": {}}, {"path": "Report/LegalStatusSection/Shareholders/Shareholder/Nationality", "tag": "Nationality", "text": "Angolan", "attributes": {}}, {"path": "Report/LegalStatusSection/Shareholders/Shareholder/NoOfShares", "tag": "NoOfShares", "text": "60", "attributes": {}}, {"path": "Report/LegalStatusSection/Shareholders/Shareholder/Shareholding", "tag": "Shareholding", "text": "60.00", "attributes": {}}, {"path": "Report/LegalStatusSection/Shareholders/Shareholder/Name", "tag": "Name", "text": "test 1", "attributes": {}}, {"path": "Report/LegalStatusSection/Shareholders/Shareholder/Nationality", "tag": "Nationality", "text": "Azerbaijani", "attributes": {}}, {"path": "Report/LegalStatusSection/Shareholders/Shareholder/NoOfShares", "tag": "NoOfShares", "text": "10", "attributes": {}}, {"path": "Report/LegalStatusSection/Shareholders/Shareholder/Shareholding", "tag": "Shareholding", "text": "30.00", "attributes": {}}, {"path": "Report/LegalStatusSection/Shareholders/Shareholder/Name", "tag": "Name", "text": "test 3", "attributes": {}}, {"path": "Report/LegalStatusSection/Shareholders/Shareholder/Nationality", "tag": "Nationality", "text": "Austrian", "attributes": {}}, {"path": "Report/LegalStatusSection/Shareholders/Shareholder/NoOfShares", "tag": "NoOfShares", "text": "40", "attributes": {}}, {"path": "Report/LegalStatusSection/Shareholders/Shareholder/Shareholding", "tag": "Shareholding", "text": "40.00", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Parent", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "Angular UAT Testing", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "998279", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/DateOccupiedFrom", "tag": "DateOccupiedFrom", "text": "30-Nov-2023", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/DateOccupiedTo", "tag": "DateOccupiedTo", "text": "23-Nov-2030", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/CareOf", "tag": "CareOf", "text": "Test0.3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Building", "tag": "Building", "text": "Test0.3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/BuildingCommercialName", "tag": "BuildingCommercialName", "text": "Test0.3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/BuildingOwner", "tag": "BuildingOwner", "text": "Test0.3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Street", "tag": "Street", "text": "Test0.3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Area", "tag": "Area", "text": "Test0.3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/POBox", "tag": "POBox", "text": "Test0.3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Town", "tag": "Town", "text": "Test0.3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/PostCode", "tag": "PostCode", "text": "1234", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "PG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ADM", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Admiralty Islands", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/GPSLocation", "tag": "GPSLocation", "text": "tudip", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/OccupancyType", "tag": "OccupancyType", "text": "Own", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Telephone/InternationalDialingCode", "tag": "InternationalDialingCode", "text": "213", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Telephone/Number", "tag": "Number", "text": "3333333", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Telephone/Extension", "tag": "Extension", "text": "11", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Telephone/Type", "tag": "Type", "text": "Fax", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/Type", "tag": "Type", "text": "Short Form", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/EnglishName", "tag": "EnglishName", "text": "3433Test...33", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/Type", "tag": "Type", "text": "Trading Style", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/EnglishName", "tag": "EnglishName", "text": "Test0.3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/Type", "tag": "Type", "text": "Trading Style", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/EnglishName", "tag": "EnglishName", "text": "Tudip0.3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/DateStarted", "tag": "DateStarted", "text": "11-Apr-2020", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/CompanyStatus", "tag": "CompanyStatus", "text": "Active", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/History", "tag": "History", "text": "Test0.3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberName", "tag": "ICPRegistrationNumberName", "text": "Other Registration Number", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/LocalRegistrationNumberName", "tag": "LocalRegistrationNumberName", "text": "<PERSON> Test2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/LocalAbbreviation", "tag": "LocalAbbreviation", "text": "AT2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/IssuingAuthority", "tag": "IssuingAuthority", "text": "456789", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/RegistrationNumberValue", "tag": "RegistrationNumberValue", "text": "32433333", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/DateIssued", "tag": "DateIssued", "text": "07-Mar-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/DateExpired", "tag": "DateExpired", "text": "29-Mar-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/RenewalFrequency", "tag": "RenewalFrequency", "text": "Annually", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/Comments", "tag": "Comments", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberTypeId", "tag": "ICPRegistrationNumberTypeId", "text": "-1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberName", "tag": "ICPRegistrationNumberName", "text": "Other Registration Number2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/LocalRegistrationNumberName", "tag": "LocalRegistrationNumberName", "text": "<PERSON> Test1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/LocalAbbreviation", "tag": "LocalAbbreviation", "text": "AT1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/RegistrationNumberValue", "tag": "RegistrationNumberValue", "text": "345678", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/DateIssued", "tag": "DateIssued", "text": "18-Mar-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/DateExpired", "tag": "DateExpired", "text": "29-Mar-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/RenewalFrequency", "tag": "RenewalFrequency", "text": "Annually", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberTypeId", "tag": "ICPRegistrationNumberTypeId", "text": "2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberName", "tag": "ICPRegistrationNumberName", "text": "Other Registration Number1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/LocalRegistrationNumberName", "tag": "LocalRegistrationNumberName", "text": "Licence No.", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/LocalAbbreviation", "tag": "LocalAbbreviation", "text": "BLN abb", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/IssuingAuthority", "tag": "IssuingAuthority", "text": "Afghanistan Central Business Registry", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/RegistrationNumberValue", "tag": "RegistrationNumberValue", "text": "3456789", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/DateIssued", "tag": "DateIssued", "text": "06-Mar-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/DateExpired", "tag": "DateExpired", "text": "28-Mar-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/RenewalFrequency", "tag": "RenewalFrequency", "text": "Annually", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberTypeId", "tag": "ICPRegistrationNumberTypeId", "text": "1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/ICPLegalGroup", "tag": "ICPLegalGroup", "text": "Government", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatusCode", "tag": "LegalStatusCode", "text": "2088", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatus", "tag": "LegalStatus", "text": "Government Agency", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatusAdditional", "tag": "LegalStatusAdditional", "text": "Test0.3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheetFigures/AccountType", "tag": "AccountType", "text": "Consolidated (Subject & Subsidiaries)", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheetFigures/CompanyName", "tag": "CompanyName", "text": "Angular UAT Testing", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheetFigures/ScaleTheme", "tag": "ScaleTheme", "text": "Units", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/FiscalYear", "tag": "FiscalYear", "text": "2012", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/FiscalYearEndDate", "tag": "FiscalYearEndDate", "text": "05 July", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/InformationType", "tag": "InformationType", "text": "Extract", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/InformationLevel", "tag": "InformationLevel", "text": "Projected", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/FinancialPeriodEndDate", "tag": "FinancialPeriodEndDate", "text": "05-Jul-2012", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/NumberOfMonths", "tag": "NumberOfMonths", "text": "5", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/Currency", "tag": "<PERSON><PERSON><PERSON><PERSON>", "text": "ANG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/Scale", "tag": "Scale", "text": "Units", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Assets", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Current Assets", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Tax Asset", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "test2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "69.00", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level1", "tag": "Level1", "text": "Assets", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level2", "tag": "Level2", "text": "Non Current Assets", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level3", "tag": "Level3", "text": "Deferred Tax Asset", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/Amount", "tag": "Amount", "text": "20.00", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossFigures/AccountType", "tag": "AccountType", "text": "Consolidated (Subject & Subsidiaries)", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossFigures/CompanyName", "tag": "CompanyName", "text": "Angular UAT Testing", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossFigures/ScaleTheme", "tag": "ScaleTheme", "text": "Units", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/FiscalYear", "tag": "FiscalYear", "text": "2023", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/FiscalYearEndDate", "tag": "FiscalYearEndDate", "text": "05 May", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/InformationType", "tag": "InformationType", "text": "Extract", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/InformationLevel", "tag": "InformationLevel", "text": "Partial", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/FinancialPeriodEndDate", "tag": "FinancialPeriodEndDate", "text": "05-May-2023", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/NumberOfMonths", "tag": "NumberOfMonths", "text": "4", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/Currency", "tag": "<PERSON><PERSON><PERSON><PERSON>", "text": "AMD", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/Scale", "tag": "Scale", "text": "Units", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Gross Profit", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Gross Profit", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Gross Profit", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Subtotal", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Gross Profit", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "3000.00", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "11", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Operating Expenses", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Operating Expenses", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Total Operating Expenses", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Subtotal", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Total Operating Expenses", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "3000.00", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "12", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Adjusted P&L", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Adjusted P&L", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Total Adjusted Profit", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Total", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Total Adjusted Profit", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "2332000.00", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "16", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Operating Profit", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Operating Profit", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Total Operating Profit", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Subtotal", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Total Operating Profit", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "3000.00", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "13", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Net P&L", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Profit Before Tax", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Profit Before Tax", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Subtotal", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Profit Before Tax", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "2324000.00", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "14", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Profit After Tax", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Net Profit", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Taxation", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "red3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "3000.00", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "11", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Operating Expenses", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Operating Expenses", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Operating Non-Recurring", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "ewrf", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "3000.00", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "8", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Profit After Tax", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Net Profit", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Net Profit", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Subtotal", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Net Profit", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "23000.00", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "15", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Net P&L", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Profit Before Tax", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Finance Costs", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "wefwe", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "232000.00", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "10", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Direct Costs", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Direct Costs", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Cost of Sales", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "df", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "4000.00", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "7", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Income", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Income", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Income Other", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Line Item", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "sd", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "45000.00", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/CategoryId", "tag": "CategoryId", "text": "6", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level1", "tag": "Level1", "text": "Direct Costs", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level2", "tag": "Level2", "text": "Total Direct Costs", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level3", "tag": "Level3", "text": "Total Direct Costs", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Level4", "tag": "Level4", "text": "Subtotal", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/SourceDescription", "tag": "SourceDescription", "text": "Total Direct Costs", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/Amount", "tag": "Amount", "text": "4000.00", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/KeyFinancialId", "tag": "KeyFinancialId", "text": "10", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/ProfitAndLossAccts/ProfitAndLossAcct/LineItems/LineItem/LineItemTypeId", "tag": "LineItemTypeId", "text": "2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesFinancialSection/AdditionalInfo", "tag": "AdditionalInfo", "text": "null\n\nhh", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "Lite Report Test RE", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999516", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/CompanyStatus", "tag": "CompanyStatus", "text": "Active", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/ICPLegalGroup", "tag": "ICPLegalGroup", "text": "Civil Company/Partnership - Non-Commercial", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatusCode", "tag": "LegalStatusCode", "text": "1011", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatus", "tag": "LegalStatus", "text": "Civil Company", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Branch", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 11", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999768", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Angola", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Branch", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 11", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999769", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Angola", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 12", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999770", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "DZ", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Algeria", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Branch", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 13", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999771", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AI", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGU", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "<PERSON><PERSON><PERSON>", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Branch", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 16", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999774", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "DZ", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Algeria", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 19", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999777", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "DZ", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Algeria", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Subsidiary", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 20", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999778", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Region", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AL", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALB", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Albania", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Subsidiary", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 21", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999779", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 31", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999785", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Region", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AD", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "AND", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Andorra", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 345", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999789", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Angola", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 345", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999790", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Angola", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Branch", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 35", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999791", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AI", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGU", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "<PERSON><PERSON><PERSON>", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Branch", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 35", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999792", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AI", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGU", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "<PERSON><PERSON><PERSON>", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 37", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999796", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Region", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AL", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALB", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Albania", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Branch", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 388", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999797", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Region", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AD", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "AND", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Andorra", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 390", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999811", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Region", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AL", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALB", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Albania", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Subsidiary", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 40", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999812", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Region", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AD", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "AND", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Andorra", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Subsidiary", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 42", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999840", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/DateOccupiedFrom", "tag": "DateOccupiedFrom", "text": "06-Jan-2025", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/DateOccupiedTo", "tag": "DateOccupiedTo", "text": "06-Jan-2025", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Angola", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 43", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999842", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/DateOccupiedFrom", "tag": "DateOccupiedFrom", "text": "08-Jan-2025", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/DateOccupiedTo", "tag": "DateOccupiedTo", "text": "10-Jan-2025", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/CareOf", "tag": "CareOf", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Building", "tag": "Building", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/BuildingCommercialName", "tag": "BuildingCommercialName", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 7", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999763", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Angola", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 8", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999764", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 8", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999765", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test 8", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999766", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test14", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999772", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Region", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AL", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALB", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Albania", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test15", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999773", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AI", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGU", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "<PERSON><PERSON><PERSON>", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test28", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999782", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test299", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999783", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Region", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AD", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "AND", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Andorra", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test30", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999784", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Angola", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Shareholder", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test322", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999786", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Shareholder", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test322", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999787", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Shareholder", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test322", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999788", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Branch", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test36", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999793", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Branch", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test36", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999794", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Branch", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "test36", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999795", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Subsidiary", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "TestForDeleteVersion0.2RE", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999300", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/DateOccupiedFrom", "tag": "DateOccupiedFrom", "text": "13-Aug-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/DateOccupiedTo", "tag": "DateOccupiedTo", "text": "24-Aug-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/CareOf", "tag": "CareOf", "text": "Test0th", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Building", "tag": "Building", "text": "Test0th", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/BuildingCommercialName", "tag": "BuildingCommercialName", "text": "Test0th", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/BuildingOwner", "tag": "BuildingOwner", "text": "Test0th", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Street", "tag": "Street", "text": "Test0th", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Area", "tag": "Area", "text": "Test0th", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/POBox", "tag": "POBox", "text": "Test0th", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/Town", "tag": "Town", "text": "Test0th", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "IN", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "IND", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "India", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Telephone/Number", "tag": "Number", "text": "4353535", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/Type", "tag": "Type", "text": "Formerly Known", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesTradingStyles/TradingStyle/EnglishName", "tag": "EnglishName", "text": "*********", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/DateStarted", "tag": "DateStarted", "text": "13-Mar-2023", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/CompanyStatus", "tag": "CompanyStatus", "text": "Active", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/History", "tag": "History", "text": "Test12", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberName", "tag": "ICPRegistrationNumberName", "text": "Business Registration Number", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/LocalRegistrationNumberName", "tag": "LocalRegistrationNumberName", "text": "test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/RegistrationNumberValue", "tag": "RegistrationNumberValue", "text": "123221", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/RegistrationNumbers/RegistrationNumber/ICPRegistrationNumberTypeId", "tag": "ICPRegistrationNumberTypeId", "text": "4", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/ICPLegalGroup", "tag": "ICPLegalGroup", "text": "Institution", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatusCode", "tag": "LegalStatusCode", "text": "7601", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatus", "tag": "LegalStatus", "text": "Financial Institution", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesLegalStatusSection/LegalStatusAdditional", "tag": "LegalStatusAdditional", "text": "Test", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing 17", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999775", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AI", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGU", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "<PERSON><PERSON><PERSON>", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Shareholder", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing 188", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999776", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Angola", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing 2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999751", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing 2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999752", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing 2", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999753", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing 5", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999758", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "DZ", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Algeria", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing 5", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999759", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "DZ", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Algeria", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing 5", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999760", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "DZ", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Algeria", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing 5", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999761", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "DZ", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Algeria", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing 6", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999762", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Region", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AL", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALB", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Albania", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing 9", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999767", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Region", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AL", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ALB", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Albania", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Subsidiary", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing duplicate 1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999839", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Angola", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Affiliate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing1", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999750", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/DateOccupiedFrom", "tag": "DateOccupiedFrom", "text": "01-Nov-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/DateOccupiedTo", "tag": "DateOccupiedTo", "text": "02-Nov-2024", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AI", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGU", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "<PERSON><PERSON><PERSON>", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Branch", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999754", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AI", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGU", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "<PERSON><PERSON><PERSON>", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Branch", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999755", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AI", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGU", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "<PERSON><PERSON><PERSON>", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Branch", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing3", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999756", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AI", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGU", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "<PERSON><PERSON><PERSON>", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Branch", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testing4", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999757", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testt 24", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999780", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANGO", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Angola", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/Relationship", "tag": "Relationship", "text": "Associate", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CompanyName", "tag": "CompanyName", "text": "testt 25", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/CSRNumber", "tag": "CSRNumber", "text": "999781", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ActiveAddress", "tag": "ActiveAddress", "text": "true", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "AG", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "ANT", "attributes": {}}, {"path": "Report/RelatedEntitiesSection/RelatedEntity/RelatedEntitiesAddressSection/Address/ICPCountryName", "tag": "ICPCountryName", "text": "Antigua", "attributes": {}}, {"path": "Report/SpecialNotesSection/TransferToPrincipal", "tag": "TransferToPrincipal", "text": "false", "attributes": {}}, {"path": "Report/WaiverSection/Waiver", "tag": "Waiver", "text": "All information within this report is provided entirely on an 'as is' basis and is used by the recipient at its sole risk. ICP shall use reasonable skill and care in providing information contained within this report and, specifically, in collecting and collating the information from reliable sources and will have no legal responsibility for any error or omission contained within this report or subsequent change to the information. It does not give any warranty or guarantee to information provided or consequences of using the information within this report for decision making purposes.\n\nInformation is to be stored and used in accordance with data protection regulation and will not be supplied, published, displayed, re-sold, copied or made known to the subject without prior agreement from ICP.", "attributes": {}}], "root_element": "Report", "namespace": {}, "processing_timestamp": "2025-07-11T18:42:25.377156"}