{"validation_id": "8c57efde-6aba-4c45-9cd1-50afd627ce80", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181493", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "bf41550c-f19b-4f29-9060-4fb3e143bb67", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "87f4bc6a-d4fd-4fb0-82de-00078a5caaed", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "097f2038-54bc-4cef-95d3-f5b3d3c9b333", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name is missing; cannot verify compliance with comments and payment sections.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection", "Report/WaiverSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "534a0ce8-dfa4-40a7-adfa-9eeba20469ab", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name is missing; comments in Special Notes and Payments sections violate rules.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "8663e32b-2cba-4212-a502-008bc4dd0a49", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Cannot verify Gross Profit and Total Income values; data is missing.", "confidence_score": 0.85, "relevant_sections": [], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "123b8fa4-f25a-46d0-a347-b23d2a099c73", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "No expenses for raw materials found; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": [], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "37bdcec0-3fd1-4108-9060-2cbcf4f9a460", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Requested name matches Company Name; Client Specific Comments not needed.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "295f14e4-3d4f-483d-abe9-1718d813f0f0", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address does not match active address; Client Specific comment is missing.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ActiveAddress", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "3df434e0-dd6c-4a5d-8672-ad5fd3167403", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not found in special notes, rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "16923a86-92aa-48d9-a2a2-3fd9d38c5f86", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "License and registration numbers are missing from the report sections.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "5b33c106-b263-4837-9714-e08de3d47b12", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in special notes section.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/WaiverSection/Waiver"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "7fbd3370-8b88-439c-b4b3-793d3b8fd54c", "question_number": 12, "question": "Spelling Check", "summary": "Spelling check passed; no errors found in specified sections.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/LegalStatusSection/ICPLegalGroup"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "a7511feb-f1b9-4905-a56a-b5b91fc15865", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No adverse announcements found in significant changes or payments section.", "confidence_score": 0.9, "relevant_sections": ["Report/SignificantChanges", "Report/Payments/CreditOpinionNotes"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "7cc06db0-4e5d-4d92-a56a-516b7ac4efcb", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Validation failed: missing significant changes and payment sections.", "confidence_score": 0.9, "relevant_sections": ["Report/SignificantChangesSection", "Report/PaymentSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "b7106452-463d-4c3a-9c83-4ed01d217170", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found; compliance rule not applicable.", "confidence_score": 0.9, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "3fc503dd-d921-4aa1-99c4-a42bcd5a4700", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks relevant sector information from Subject activities.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c2126380-cf52-4685-9b7f-96c03724f055", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Violation found: Incorrect text added regarding export activities.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/WaiverSection/Waiver"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "e5109540-904f-430a-ab5c-94d56bbe8116", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income are both missing, compliance violated.", "confidence_score": 0.98, "relevant_sections": ["Report/ProfitLossSection/Income", "Report/ProfitLossSection/TotalIncome"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "768d1c24-f2af-4801-b984-e8a8fb99a814", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Credit opinion not requested; compliance rule not met.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "7b6de68a-ccbf-45c2-9a7c-1fccd90b209e", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not added where applicable, violation found.", "confidence_score": 0.92, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ISO2CountryCode", "Report/AddressesSection/Addresses/Address/ICPCountryCode"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "46e00e29-36ef-4f1f-8531-1bd5573ee0aa", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Company status is 'Active', compliant with no dormant classification found.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus", "Report/AddressesSection/Addresses/Address/ActiveAddress"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "336373e2-8dd2-42f0-ab51-8045c443a369", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number missing; cannot verify compliance with branch requirements.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/LegalStatusSection/ParentCompany"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "4bb8674a-bd93-454e-b115-e9d92ba1b291", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Registration number expired with no Comment present.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "aa08aa9d-c753-45ba-97a7-ec075de44656", "question_number": 24, "question": "Small with large amount", "summary": "Validation rule violated: PaidUp amount is small compared to company size.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/LegalStatus"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "10b8bb4a-2ed7-4776-915d-f3a387b1198d", "question_number": 25, "question": "Large with small amount", "summary": "PaidUp capital is too small compared to the company's status, violating the rule.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "e00249af-0f5d-4c4a-b927-453597a65b4b", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications are not provided; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "c5541da4-3971-403c-96d2-13f93fbf4951", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Validation failed; 'Research Type' not set to 'Negative' and other sections are populated.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection", "Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "2b538070-54d5-4b03-97fd-dfd5c11c3034", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Validation failed: Research Type is not 'No Contact' as required.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "8d6b91e9-1ce0-4546-9ce7-d77f23ed652b", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Research type is not 'Decline'; compliance rule not met.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/PaymentsSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "35a8ef78-1909-49b9-90e1-a144705994ba", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Validation failed: Research Type is blank, missing comments and Person Interviewed.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/Payments/CreditOpinionNotes", "Report/SpecialNotesSection/PersonInterviewed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "ecff3cbe-07a8-4f96-afb0-d69a7812acde", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanctions search not performed; required tag is missing.", "confidence_score": 0.98, "relevant_sections": ["Report/SanctionSection/SanctionSearchPerformed"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "194cde38-5a5c-457d-8c21-e2e70bb1c219", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Sanctions not found; compliance rule violated.", "confidence_score": 0.9, "relevant_sections": ["Report/SanctionSection/SanctionSearchPerformed", "Report/SanctionSection/SanctionFound"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "4962a9a6-a443-40b4-ae03-c6d81c9b76f7", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "8c021fcd-5967-4708-be38-239658f975ec", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in Header and LegalStatus sections.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "ede34e61-e0e4-4133-8c9c-fa76a3ffd768", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Contact details missing; no telephone or email in the address section.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/AddressesSection/Addresses/Address"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "4319684e-ed01-4d08-b9ad-e2f459bdc8cf", "question_number": 36, "question": "Capital missing currency", "summary": "Capital currency field is missing; expected blank notification.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/Capital/NotGiven"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "fdf13943-1610-44c9-a533-1d62621c2a81", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder not populated while being part of a related entity group, rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/ParentCompany", "Report/LegalStatusSection/Subsidiaries"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "3f6d9440-ad7b-4132-945e-a12422bd2de7", "question_number": 38, "question": "Calculate credit opinion", "summary": "Cannot verify credit opinion due to missing payment data.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "00d178c5-3b66-48b3-bc90-e8626b261ca4", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Names in financials match header; compliance rule satisfied.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f59068b8-6952-49a5-a627-8e3e1f504c60", "question_number": 40, "question": "Town missing in town box", "summary": "Town box is empty; report cannot be sent for edit.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressesSection/Addresses/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "7905d0c3-de2a-4c4a-a98f-53ee2e1b1631", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status is known as 'Active'; credit can be granted.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-11 15:40:49.219151", "processing_time": 14.***************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "ONLINEMISC", "rag_enabled": true, "permanent_question_bank_used": true}