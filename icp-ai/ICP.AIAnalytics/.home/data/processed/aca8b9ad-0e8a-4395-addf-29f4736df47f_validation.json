{"validation_id": "aca8b9ad-0e8a-4395-addf-29f4736df47f", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "178980", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "63a0b079-04c0-49e0-a5cd-d39d1e318a5d", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'EHTR'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "1346520e-36fb-4116-ab1e-dc4c0d6952aa", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'EHTR'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "a8bb1bdd-ced7-4d88-9ba1-d68227951fe6", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name 'MOGZAURI PLUS T/A MPLUS' present but missing comments and payment details", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "971b23b8-5786-49b7-ae18-f1f897ecbe4e", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name absent, no notes found in comments or payment sections.", "confidence_score": 0.95, "relevant_sections": ["Order Details", "Payments", "Special Notes"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "062cd796-c601-4e9a-bd7a-d0bd23e1f246", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Cannot verify Gross Profit vs Total Income due to missing data", "confidence_score": 0.85, "relevant_sections": ["Financial/ProfitAndLoss", "Financial/TotalIncome", "Financial/TotalGrossProfit"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "5eae1241-95b5-4843-8a71-f99a30617c8b", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Cannot verify Expenses placement due to missing financial data", "confidence_score": 0.85, "relevant_sections": ["Report"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "9cecf6a8-407a-4476-8f36-01460d74ac15", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Company name 'MOGZAURI PLUS T/A MPLUS' matches requested name 'MOGZAURI PLUS T/A MPLUS'", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d5e5568d-ee20-4b5b-9846-aedf6a30b6d0", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client name 'MOGZAURI PLUS T/A MPLUS' present but Client Specific Comments empty", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "74730920-bf28-4b0a-b1f2-7b82b0e77430", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not found in special note; violates rule for EUR, USD, GBP", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "135aae45-9e47-477f-a0be-442d95e5fb80", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "Registration/License numbers missing in LegalStatus or SpecialNotes", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "a0df7a71-049a-4960-ac4e-7e6065f7e817", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language in Payments activities special note", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c7ffef95-5af9-4aa5-9eee-2ed9ae096426", "question_number": 12, "question": "Spelling Check", "summary": "No spelling errors detected in report content", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection", "Report/WaiverSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "38851d70-abe8-4b89-93b4-bd4dd1f7441d", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No significant changes or payment references found in XML", "confidence_score": 0.9, "relevant_sections": ["Report"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "bc62f862-429e-47bb-babb-4aa1eafd23e2", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Cannot verify adverse announcements in significant changes/payments sections", "confidence_score": 0.85, "relevant_sections": ["Report/SignificantChanges", "Report/Payments"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f34a053f-b15e-46b5-af8e-32cdc177296b", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found in Change Classifications or Descriptions", "confidence_score": 0.9, "relevant_sections": ["Report/SignificantChanges/ChangeClassifications", "Report/SignificantChanges/ChangeDescription"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d1427598-40a0-4d15-bc00-48f581916ebf", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section missing; cannot verify sector relevance.", "confidence_score": 0.9, "relevant_sections": ["Report/Payments"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "a16d3e76-250b-488b-b30b-88c3035abf65", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "No service activities text found, cannot verify export text compliance", "confidence_score": 0.85, "relevant_sections": ["Report"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8c78942a-7adc-4242-8ea3-0933e68f38ff", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income elements missing, violates validation rule", "confidence_score": 0.95, "relevant_sections": ["Financial/Profit and Loss", "Income", "Total Income"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "546a9371-6e17-469a-ae58-c32036007e55", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Currency not specified, TRA high not verified", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection", "Report/LegalStatusSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "e03f9168-5dfb-4512-937a-9d19ff955362", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not found in Payment section, compliant", "confidence_score": 0.95, "relevant_sections": ["Report"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "0478ffae-b5ef-4d45-9d2e-df82c15e4ecd", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "No classification for dormant/ceased in Payment section found.", "confidence_score": 0.9, "relevant_sections": ["Report"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5ed7798e-b960-417e-8bb6-d045bb30247c", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number not listed as Branch, no special note, Transfer to Principal false", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2d389418-867b-4760-bb4e-b4635e41c355", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "No registration number or expiry comment found, violates rule", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "316fb36f-2ad3-4d34-9e51-8d578699f2af", "question_number": 24, "question": "Small with large amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1674. Please try again in 3.348s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "5c3de526-05b4-43a4-b601-d7cea0f23ac0", "question_number": 25, "question": "Large with small amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1674. Please try again in 3.348s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "31aef2bf-7cce-4958-b2bc-079fd5819bae", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications missing; cannot verify compliance", "confidence_score": 0.9, "relevant_sections": ["Personnel/Position", "PositionClassifications"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "59123e9e-2c15-4e89-b5cc-96397e7f283b", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Research type 'Negative' but LegalStatusSection populated, violates rule", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "23fe56ed-4f93-4434-a16c-fc526a5db63a", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Research type 'Decline' not 'No Contact', Person Interviewed check not applicable", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f6387e79-b6fb-4ea6-9835-18e1e02712ea", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Research type 'Decline' missing comment in Payments section", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/Payments"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "17c3376f-2492-4b15-8287-03288d24f703", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Research Type missing, no comment or Person Interviewed found", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "d1812c3a-d978-40c7-a22b-b1e62f36b31e", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1724. Please try again in 3.448s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "d0ba6a9c-f5f8-428f-8762-e561dc2e3d80", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1764. Please try again in 3.528s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "386871ad-c4cc-4cdb-9bbd-************", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'EHTR'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "b90244c5-c778-47b8-8ebc-445a1ea9e642", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1735. Please try again in 3.47s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "ba39614c-91d2-4a8b-9d4c-ba0f999220a3", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1753. Please try again in 3.506s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "864a0b33-24b0-4c73-af3d-e77e9f18f9ba", "question_number": 36, "question": "Capital missing currency", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1717. Please try again in 3.434s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "71dcf994-0b69-4c7b-a413-e6a7e82466a5", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1661. Please try again in 3.322s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "5cd7f821-397d-4a3d-b003-3e6d7d1cdfd0", "question_number": 38, "question": "Calculate credit opinion", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1709. Please try again in 3.418s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "dec1cf59-2030-4f20-9ca1-084d7e4642ae", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1734. Please try again in 3.468s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "6a5cf65b-d2ec-407c-a9cb-f6f1453b8d6a", "question_number": 40, "question": "Town missing in town box", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1729. Please try again in 3.458s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "e99e7b0e-c18e-40a1-b8dd-7efdd6d59176", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1726. Please try again in 3.452s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-14 18:37:53.190880", "processing_time": 31.***************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "EHTR", "rag_enabled": true, "permanent_question_bank_used": true}