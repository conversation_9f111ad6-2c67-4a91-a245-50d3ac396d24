{"file_id": "a38f9f14-9d95-4d19-b3dc-e4fbe242a214", "xml_structure": {"Report": {"@xmlns:xsd": "http://www.w3.org/2001/XMLSchema", "@xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "HeaderSection": {"Date": "01-Jul-2025", "DeliveryDate": "01-Jul-2025", "OurRef": "1000368.1.2", "YourRef": "1", "CompanyName": "GenAI25", "Requested": "GenAI25"}, "AddressesSection": {"Addresses": {"Address": {"ActiveAddress": "false", "BuildingOwner": "test", "Street": "test", "Area": "test", "POBox": "test", "ISO2CountryCode": "IN", "ICPCountryCode": "IND", "ICPCountryName": "India"}}}, "LegalStatusSection": {"DateStarted": "01-Jan-2025", "CompanyStatus": "Active", "Capital": {"PaidUp": "123.00", "NotGiven": "false"}, "ICPLegalGroup": "Civil Company/Partnership - Non-Commercial", "LegalStatusCode": "3006", "LegalStatus": "Civil Partnership", "LegalStatusAdditional": "test", "ParentCompany": "test", "Subsidiaries": "asdasd"}, "SpecialNotesSection": {"TransferToPrincipal": "false"}, "WaiverSection": {"Waiver": "All information within this report is provided entirely on an 'as is' basis and is used by the recipient at its sole risk. ICP shall use reasonable skill and care in providing information contained within this report and, specifically, in collecting and collating the information from reliable sources and will have no legal responsibility for any error or omission contained within this report or subsequent change to the information. It does not give any warranty or guarantee to information provided or consequences of using the information within this report for decision making purposes.\n\nInformation is to be stored and used in accordance with data protection regulation and will not be supplied, published, displayed, re-sold, copied or made known to the subject without prior agreement from ICP."}}}, "text_content": [{"path": "Report/HeaderSection/Date", "tag": "Date", "text": "01-Jul-2025", "attributes": {}}, {"path": "Report/HeaderSection/DeliveryDate", "tag": "DeliveryDate", "text": "01-Jul-2025", "attributes": {}}, {"path": "Report/HeaderSection/OurRef", "tag": "OurRef", "text": "1000368.1.2", "attributes": {}}, {"path": "Report/HeaderSection/YourRef", "tag": "YourRef", "text": "1", "attributes": {}}, {"path": "Report/HeaderSection/CompanyName", "tag": "CompanyName", "text": "GenAI25", "attributes": {}}, {"path": "Report/HeaderSection/Requested", "tag": "Requested", "text": "GenAI25", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/ActiveAddress", "tag": "ActiveAddress", "text": "false", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/BuildingOwner", "tag": "BuildingOwner", "text": "test", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/Street", "tag": "Street", "text": "test", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/Area", "tag": "Area", "text": "test", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/POBox", "tag": "POBox", "text": "test", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "IN", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/ICPCountryCode", "tag": "ICPCountryCode", "text": "IND", "attributes": {}}, {"path": "Report/AddressesSection/Addresses/Address/ICPCountryName", "tag": "ICPCountryName", "text": "India", "attributes": {}}, {"path": "Report/LegalStatusSection/DateStarted", "tag": "DateStarted", "text": "01-Jan-2025", "attributes": {}}, {"path": "Report/LegalStatusSection/CompanyStatus", "tag": "CompanyStatus", "text": "Active", "attributes": {}}, {"path": "Report/LegalStatusSection/Capital/PaidUp", "tag": "PaidUp", "text": "123.00", "attributes": {}}, {"path": "Report/LegalStatusSection/Capital/NotGiven", "tag": "NotGiven", "text": "false", "attributes": {}}, {"path": "Report/LegalStatusSection/ICPLegalGroup", "tag": "ICPLegalGroup", "text": "Civil Company/Partnership - Non-Commercial", "attributes": {}}, {"path": "Report/LegalStatusSection/LegalStatusCode", "tag": "LegalStatusCode", "text": "3006", "attributes": {}}, {"path": "Report/LegalStatusSection/LegalStatus", "tag": "LegalStatus", "text": "Civil Partnership", "attributes": {}}, {"path": "Report/LegalStatusSection/LegalStatusAdditional", "tag": "LegalStatusAdditional", "text": "test", "attributes": {}}, {"path": "Report/LegalStatusSection/ParentCompany", "tag": "ParentCompany", "text": "test", "attributes": {}}, {"path": "Report/LegalStatusSection/Subsidiaries", "tag": "Subsidiaries", "text": "asdasd", "attributes": {}}, {"path": "Report/SpecialNotesSection/TransferToPrincipal", "tag": "TransferToPrincipal", "text": "false", "attributes": {}}, {"path": "Report/WaiverSection/Waiver", "tag": "Waiver", "text": "All information within this report is provided entirely on an 'as is' basis and is used by the recipient at its sole risk. ICP shall use reasonable skill and care in providing information contained within this report and, specifically, in collecting and collating the information from reliable sources and will have no legal responsibility for any error or omission contained within this report or subsequent change to the information. It does not give any warranty or guarantee to information provided or consequences of using the information within this report for decision making purposes.\n\nInformation is to be stored and used in accordance with data protection regulation and will not be supplied, published, displayed, re-sold, copied or made known to the subject without prior agreement from ICP.", "attributes": {}}], "root_element": "Report", "namespace": {}, "processing_timestamp": "2025-07-11T11:25:41.578172"}