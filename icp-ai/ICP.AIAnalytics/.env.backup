OPENAI_API_KEY=********************************************************************************************************************************************************************
APP_NAME=XML Report Validation System
DEBUG=True
HOST=0.0.0.0
PORT=8000
OPENAI_MODEL=gpt-4o-mini

# Performance Configuration
DEFAULT_BATCH_SIZE=10
MAX_SEARCH_RESULTS=3
USE_BULK_PROCESSING=true
ENABLE_PERFORMANCE_OPTIMIZATIONS=true

# ChromaDB Setup
CHROMADB_MODE=server 
CHROMADB_HOST=localhost 
CHROMADB_PORT=8001