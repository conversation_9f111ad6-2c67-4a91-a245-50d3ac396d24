import pytest
from unittest.mock import AsyncMock, patch


class TestClientCodeExtraction:
    """Test client code extraction logic."""

    def test_extract_client_short_code_priority(self):
        """Test that clientShortCode is prioritized over other client fields."""
        # Test data with multiple client fields
        api_data = {
            "clientId": 383,
            "clientShortCode": "0447",  # This should be selected
            "clientCode": "OLD_CODE",   # This should be ignored
            "clientReference": "tudip1Report",
            "companyName": "tudip1"
        }

        # Simulate the field priority logic from validation_service.py
        possible_client_keys = [
            'clientShortCode',  # Primary field based on API response
            'clientCode', 'client_code', 'ClientCode', 'Client_Code',
            'client', 'Client', 'clientId', 'ClientId', 'client_id'
        ]

        client_code = None
        for key in possible_client_keys:
            if key in api_data:
                client_code = str(api_data[key])
                break

        assert client_code == "0447"

    def test_extract_fallback_to_client_code(self):
        """Test fallback to clientCode when clientShortCode is not present."""
        # Test data without clientShortCode
        api_data = {
            "clientId": 383,
            "clientCode": "LEGACY_CODE",
            "clientReference": "tudip1Report",
            "companyName": "tudip1"
        }

        # Simulate the field priority logic
        possible_client_keys = [
            'clientShortCode',  # Not present
            'clientCode', 'client_code', 'ClientCode', 'Client_Code',
            'client', 'Client', 'clientId', 'ClientId', 'client_id'
        ]

        client_code = None
        for key in possible_client_keys:
            if key in api_data:
                client_code = str(api_data[key])
                break

        assert client_code == "LEGACY_CODE"

    def test_extract_no_client_fields(self):
        """Test when no client fields are present."""
        # Test data without any client fields
        api_data = {
            "orderId": 12345,
            "companyName": "tudip1",
            "country": "India"
        }

        # Simulate the field priority logic
        possible_client_keys = [
            'clientShortCode',
            'clientCode', 'client_code', 'ClientCode', 'Client_Code',
            'client', 'Client', 'clientId', 'ClientId', 'client_id'
        ]

        client_code = None
        for key in possible_client_keys:
            if key in api_data:
                client_code = str(api_data[key])
                break

        assert client_code is None

    def test_extract_nested_client_fields(self):
        """Test extraction from nested objects."""
        # Test data with nested client information
        api_data = {
            "orderId": 12345,
            "clientInfo": {
                "clientShortCode": "NESTED_0447",
                "clientCode": "NESTED_OLD"
            },
            "companyName": "tudip1"
        }

        # Simulate the nested search logic
        possible_client_keys = [
            'clientShortCode',
            'clientCode', 'client_code', 'ClientCode', 'Client_Code',
            'client', 'Client', 'clientId', 'ClientId', 'client_id'
        ]

        client_code = None
        
        # First search in root
        for key in possible_client_keys:
            if key in api_data:
                client_code = str(api_data[key])
                break
        
        # If not found in root, search nested objects
        if not client_code:
            for key, value in api_data.items():
                if isinstance(value, dict):
                    for nested_key in possible_client_keys:
                        if nested_key in value:
                            client_code = str(value[nested_key])
                            break
                    if client_code:
                        break

        assert client_code == "NESTED_0447"

    def test_real_api_response_structure(self):
        """Test with the actual API response structure provided by the user."""
        api_response = {
            "clientId": 383,
            "clientShortCode": "0447",
            "clientReference": "tudip1Report",
            "country": "India",
            "speed": "NORMAL",
            "orderDate": "2025-05-20T00:00:00",
            "clientDeliveryDate": "2025-05-29T00:00:00",
            "websiteLoginEmail": "",
            "webRefNumber": "",
            "companyName": "tudip1",
            "companyNameReceived": "",
            "address": "India",
            "orderEnteredBy": "TARA",
            "creditRequested": "",
            "isSentToCorrespondent": False,
            "correspondentName": None,
            "licenseNumber": "",
            "subjectTelephoneNumber": "",
            "subjectEmailAddress": "",
            "subjectKeyPersonnelGiven": "",
            "discloseClientName": False,
            "clientName": "",
            "otherCSR": "",
            "supplierName": "",
            "clientCommentIncludedInReport": "",
            "clientCommentNotIncludedInReport": "",
            "researcherNotes": ""
        }

        # Simulate the extraction logic
        possible_client_keys = [
            'clientShortCode',  # Primary field based on API response
            'clientCode', 'client_code', 'ClientCode', 'Client_Code',
            'client', 'Client', 'clientId', 'ClientId', 'client_id'
        ]

        client_code = None
        for key in possible_client_keys:
            if key in api_response:
                client_code = str(api_response[key])
                break

        assert client_code == "0447"

    def test_question_filtering_logic(self):
        """Test the question filtering logic."""
        from app.models.schemas import Question
        
        # Mock questions with different client codes
        questions = [
            Question(id="1", question="Question 1", client_code="0447"),
            Question(id="2", question="Question 2", client_code="OTHER"),
            Question(id="3", question="Question 3", client_code=None),
            Question(id="4", question="Question 4", client_code="0447"),
            Question(id="5", question="Question 5")  # No client_code field
        ]
        
        order_client_code = "0447"
        
        # Simulate the filtering logic from _filter_questions_by_client
        filtered_questions = []
        skipped_count = 0
        
        for question in questions:
            if hasattr(question, 'client_code') and question.client_code:
                # If question has client code, only include if it matches
                if question.client_code.strip() == order_client_code.strip():
                    filtered_questions.append(question)
                else:
                    skipped_count += 1
            else:
                # If question has no client code, include it (applies to all clients)
                filtered_questions.append(question)
        
        # Should include questions 1, 3, 4, 5 (matching client code and no client code)
        assert len(filtered_questions) == 4
        assert skipped_count == 1  # Question 2 skipped
        
        # Check that the correct questions are included
        included_ids = [q.id for q in filtered_questions]
        assert "1" in included_ids  # Matching client code
        assert "3" in included_ids  # No client code (applies to all)
        assert "4" in included_ids  # Matching client code
        assert "5" in included_ids  # No client_code field (applies to all)
        assert "2" not in included_ids  # Different client code 