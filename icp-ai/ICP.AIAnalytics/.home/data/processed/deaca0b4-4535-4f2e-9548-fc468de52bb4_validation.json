{"validation_id": "deaca0b4-4535-4f2e-9548-fc468de52bb4", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "178980", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "76d019e4-5248-4844-93e2-92547765b7bc", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'EHTR'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "eca8ad0c-054b-4275-b7a3-f7c006fc4d49", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'EHTR'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "c12edae1-4a43-4628-b8eb-8cf27c2cde8f", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name 'MOGZAURI PLUS T/A MPLUS' present but Client Specific Comments and Payment sections missing", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "2cb1d3f9-9357-4587-85b9-ce43aa7f743e", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name missing, comments and payment notes should be empty.", "confidence_score": 0.95, "relevant_sections": ["Order Details", "Client Specific Comments", "Payment Section"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "137f72d0-a7b7-4ee5-a5cc-b0bdf09573f3", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Cannot verify Gross Profit vs Total Income as data is missing", "confidence_score": 0.9, "relevant_sections": ["Financial/Profit and Loss", "Total Income", "Total Gross Profit"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "eb61e254-5dcc-4b2b-a1f3-daa69497689b", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Expenses placement cannot be verified; financial data missing", "confidence_score": 0.85, "relevant_sections": ["Financial"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "54555c08-b2d7-4916-9b44-b0053d9b63e3", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Company name 'MOGZAURI PLUS T/A MPLUS' matches requested name 'MOGZAURI PLUS T/A MPLUS'", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "46dea40c-6e31-402e-8c2d-b8af995a07f4", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client name 'MOGZAURI PLUS T/A MPLUS' lacks Client Specific Comments", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "0bc69db9-8182-4258-8c6c-a26ad800951d", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not found in special note, violates rule", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "5a865a1f-b671-4212-bb7b-6cc8e994eeb9", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "Registration/License numbers missing in Legal or Special Notes sections", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "699b7815-211f-44d5-8858-c6adf5f3cc9a", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased/colloquial language in Payments activities special note", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d15f2bff-0a52-412b-b162-c4e1dad20c8d", "question_number": 12, "question": "Spelling Check", "summary": "No spelling errors detected in report content", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection", "Report/WaiverSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "0e6a62a2-e292-44c0-abf0-8fb31103f29b", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "Cannot verify adverse announcements in 'Significant Changes' or 'Payments'.", "confidence_score": 0.85, "relevant_sections": ["Significant Changes", "Payments"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "a21e4582-1da0-450a-a769-9f655f1911d4", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Significant changes and payment sections missing, cannot verify compliance", "confidence_score": 0.85, "relevant_sections": ["Report"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "c5598f40-9f47-4f9f-8f8d-9d06dfb57ad8", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found in Change Descriptions.", "confidence_score": 0.9, "relevant_sections": ["Report/SignificantChanges/ChangeClassifications", "Report/SignificantChanges/ChangeDescription"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "742e9479-957a-4eda-8309-dbed104fd05c", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section missing sector-related info for MOGZAURI PLUS T/A MPLUS", "confidence_score": 0.9, "relevant_sections": ["Report/PaymentsSection", "Report/SubjectActivities"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b3f57806-01da-4ba0-8db7-40d5b785ee3f", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "No export-related text errors in Activities section", "confidence_score": 0.9, "relevant_sections": ["Report"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "feef024c-64c1-435c-b88e-94fd6777391b", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income sections missing, cannot verify compliance", "confidence_score": 0.9, "relevant_sections": ["Financial/Profit and Loss", "Financial/Income", "Financial/Total Income"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "fb05e924-0a18-43ad-81ba-2cd7d6eefd67", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "No credit given, TRA high not found; cannot verify rule", "confidence_score": 0.85, "relevant_sections": ["Report/SpecialNotesSection", "Report/LegalStatusSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "509b2a4f-a8eb-41ed-b7a5-75f58a1ed3eb", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not found in Payment section", "confidence_score": 0.95, "relevant_sections": ["Report"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "274cb02d-b2f8-4d79-9b4f-182e3fee9cd0", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "No classification for dormant, ceased trading found in Payment section", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d96d4a55-efb1-4ec5-b384-cdfe350d620c", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1796. Please try again in 3.592s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "42e1aee1-ec9c-4936-96e5-54618ed809a3", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1745. Please try again in 3.49s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "e8922fb9-7ecf-4def-a8ad-fd051efa19f4", "question_number": 24, "question": "Small with large amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1674. Please try again in 3.348s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "cb729932-f86b-4f6b-aa66-c4fdba1ebdb0", "question_number": 25, "question": "Large with small amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1674. Please try again in 3.348s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "542f01fa-87bc-4577-98c0-068e0a850c74", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications not found, cannot verify compliance", "confidence_score": 0.85, "relevant_sections": ["Personnel/Position", "Position Classifications"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "66e8ae00-b7e5-400c-9f64-b50166f9d327", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Research type 'Negative' but LegalStatusSection populated, violating rule.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "b52ccb65-bb8b-45b4-8e24-6413455161cd", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Research type 'Decline' not 'No Contact', Person Interviewed check not applicable", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c9986ca3-7d21-4577-9990-afdddf8c3071", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1798. Please try again in 3.596s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "29b3e78b-72e7-4e7e-bcff-9f1945184067", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Research Type 'Select' missing, no payment comment or Person Interviewed", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b53cfdea-577c-40fd-817b-744544319c84", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanction search tag missing in XML, violates requirement", "confidence_score": 0.95, "relevant_sections": ["Sanction Search Performed"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "94c2dc27-b5e0-4e15-895f-de1b37dc2ad8", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29645, Requested 1764. Please try again in 2.818s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "320d3346-78f8-4db4-baaf-018b449b17fd", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'EHTR'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "75fd80cd-ecd6-4fb8-9bd3-7f28c71bf4f3", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29846, Requested 1735. Please try again in 3.162s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "2af475e1-10ab-4994-a8ce-f6584949c561", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29675, Requested 1753. Please try again in 2.856s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "cfec7ba7-2836-42db-80be-9229a75a278e", "question_number": 36, "question": "Capital missing currency", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29839, Requested 1717. Please try again in 3.112s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "2f7af335-03d4-4122-a3be-9afd882f7500", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29872, Requested 1661. Please try again in 3.066s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "c846d34d-dc2d-412c-a1e4-241574ed6771", "question_number": 38, "question": "Calculate credit opinion", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29812, Requested 1709. Please try again in 3.042s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "967df269-fc27-49e4-b4fa-45662434b4d5", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29597, Requested 1734. Please try again in 2.662s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "bb4da78d-c862-4f42-b6bc-f94cd1fc7aa3", "question_number": 40, "question": "Town missing in town box", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29676, Requested 1729. Please try again in 2.81s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "68a1b280-5d38-4230-b67d-b04a48b00ac7", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status 'Government Ministry' known, rule compliant", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/LegalStatus"], "status": "approved", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-14 16:57:44.343347", "processing_time": 32.***************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "EHTR", "rag_enabled": true, "permanent_question_bank_used": true}