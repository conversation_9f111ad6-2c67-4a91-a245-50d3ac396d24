{"info": {"name": "XML Report Validation System", "description": "API collection for testing XML report validation system with AI & RAG. Features three-status validation results: 'approved' (positive/satisfactory), 'rejected' (negative/unsatisfactory), and 'manual_intervention_needed' (unclear/insufficient data).", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "xml-validation-api", "version": "1.0.0"}, "auth": {"type": "<PERSON><PERSON><PERSON>"}, "variables": [{"key": "base_url", "value": "http://localhost:8000/api/v1", "type": "string"}, {"key": "questions_file_id", "value": "", "type": "string"}, {"key": "report_file_id", "value": "", "type": "string"}, {"key": "validation_id", "value": "", "type": "string"}, {"key": "report_id", "value": "172390", "type": "string"}, {"key": "bearer_token", "value": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.gQm1h0w159-PaWsXdglsq10x16h00Pw4y4kpgPPch7E", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/../../health", "host": ["{{base_url}}"], "path": ["../../health"]}}, "description": "Check API health status"}, {"name": "Upload Excel Questions", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('questions_file_id', response.file_id);", "    pm.test('Questions uploaded successfully', function () {", "        pm.expect(response.file_id).to.be.a('string');", "        pm.expect(response.total_questions).to.be.a('number');", "    });", "}"]}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}, {"key": "response_format", "value": "json", "type": "text"}]}, "url": {"raw": "{{base_url}}/upload/questions", "host": ["{{base_url}}"], "path": ["upload", "questions"]}}, "description": "Upload Excel file containing validation questions"}, {"name": "Upload XML Report", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('report_file_id', response.file_id);", "    pm.test('Report uploaded successfully', function () {", "        pm.expect(response.file_id).to.be.a('string');", "        pm.expect(response.status).to.equal('uploaded');", "    });", "}"]}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}, {"key": "response_format", "value": "json", "type": "text"}]}, "url": {"raw": "{{base_url}}/upload/report", "host": ["{{base_url}}"], "path": ["upload", "report"]}}, "description": "Upload XML report file for validation"}, {"name": "Fetch Report from External API", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('report_file_id', response.file_id);", "    pm.test('Report fetched from API successfully', function () {", "        pm.expect(response.file_id).to.be.a('string');", "        pm.expect(response.status).to.equal('uploaded');", "        pm.expect(response.source).to.equal('external_api');", "        pm.expect(response.report_id).to.equal(pm.collectionVariables.get('report_id'));", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"report_id\": \"{{report_id}}\",\n  \"bearer_token\": \"{{bearer_token}}\",\n  \"response_format\": \"json\"\n}"}, "url": {"raw": "{{base_url}}/upload/report-from-api", "host": ["{{base_url}}"], "path": ["upload", "report-from-api"]}}, "description": "Fetch XML report from external API using report ID and bearer token"}, {"name": "Validate Report (Sync)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('validation_id', response.validation_id);", "    pm.test('Validation completed', function () {", "        pm.expect(response.validation_id).to.be.a('string');", "        pm.expect(response.status).to.equal('completed');", "        pm.expect(response.results).to.be.an('array');", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"questions_file_id\": \"{{questions_file_id}}\",\n  \"report_file_id\": \"{{report_file_id}}\",\n  \"validation_options\": {\n    \"include_low_confidence\": true,\n    \"min_confidence_threshold\": 0.3\n  }\n}"}, "url": {"raw": "{{base_url}}/validate?response_format=json&async_processing=false", "host": ["{{base_url}}"], "path": ["validate"], "query": [{"key": "response_format", "value": "json"}, {"key": "async_processing", "value": "false"}]}}, "description": "Validate report synchronously"}, {"name": "Validate Report (Async)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('validation_id', response.validation_id);", "    pm.test('Async validation started', function () {", "        pm.expect(response.validation_id).to.be.a('string');", "        pm.expect(response.status).to.equal('processing');", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"questions_file_id\": \"{{questions_file_id}}\",\n  \"report_file_id\": \"{{report_file_id}}\",\n  \"validation_options\": {\n    \"batch_size\": 3,\n    \"include_low_confidence\": true\n  }\n}"}, "url": {"raw": "{{base_url}}/validate?response_format=json&async_processing=true", "host": ["{{base_url}}"], "path": ["validate"], "query": [{"key": "response_format", "value": "json"}, {"key": "async_processing", "value": "true"}]}}, "description": "Start validation in background"}, {"name": "Get Validation Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/validate/status/{{validation_id}}", "host": ["{{base_url}}"], "path": ["validate", "status", "{{validation_id}}"]}}, "description": "Check status of background validation"}, {"name": "Get Validation Result", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/validate/result/{{validation_id}}", "host": ["{{base_url}}"], "path": ["validate", "result", "{{validation_id}}"]}}, "description": "Get validation results"}, {"name": "Get Validation Result (XML)", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/validate/result/{{validation_id}}?response_format=xml", "host": ["{{base_url}}"], "path": ["validate", "result", "{{validation_id}}"], "query": [{"key": "response_format", "value": "xml"}]}}, "description": "Get validation results in XML format"}, {"name": "Get Validation Summary", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/validate/summary/{{validation_id}}", "host": ["{{base_url}}"], "path": ["validate", "summary", "{{validation_id}}"]}}, "description": "Get validation summary"}, {"name": "List Uploaded Files", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/upload/files", "host": ["{{base_url}}"], "path": ["upload", "files"]}}, "description": "List all uploaded files"}, {"name": "Get Questions by File ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/upload/questions/{{questions_file_id}}", "host": ["{{base_url}}"], "path": ["upload", "questions", "{{questions_file_id}}"]}}, "description": "Get processed questions by file ID"}, {"name": "Get Report Info", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/upload/report/{{report_file_id}}", "host": ["{{base_url}}"], "path": ["upload", "report", "{{report_file_id}}"]}}, "description": "Get report information by file ID"}, {"name": "Validation History", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/validate/history?page=1&page_size=10", "host": ["{{base_url}}"], "path": ["validate", "history"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "description": "Get validation history with pagination"}]}