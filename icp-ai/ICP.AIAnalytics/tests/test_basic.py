import pytest
import asyncio
from pathlib import Path
import json
import tempfile
import os

# Test basic imports
def test_imports():
    """Test that core modules can be imported."""
    try:
        from app.main import app
        from app.core.config import settings
        from app.services.file_processor import FileProcessor
        from app.services.validation_service import ValidationService
        from app.utils.xml_generator import XMLGenerator
        assert True
    except ImportError as e:
        pytest.fail(f"Import failed: {e}")


def test_settings():
    """Test configuration settings."""
    from app.core.config import settings
    
    assert hasattr(settings, 'APP_NAME')
    assert hasattr(settings, 'APP_VERSION')
    assert hasattr(settings, 'UPLOAD_PATH')
    assert hasattr(settings, 'PROCESSED_PATH')
    assert hasattr(settings, 'is_system_ready')


@pytest.mark.asyncio
async def test_file_processor():
    """Test basic file processor functionality."""
    from app.services.file_processor import FileProcessor
    
    processor = FileProcessor()
    
    # Test metadata functionality
    assert hasattr(processor, 'save_uploaded_file')
    assert hasattr(processor, 'process_excel_questions')
    assert hasattr(processor, 'process_xml_report')


def test_xml_generator():
    """Test XML generator functionality."""
    from app.utils.xml_generator import XMLGenerator
    
    generator = XMLGenerator()
    
    # Test error XML generation
    error_xml = generator.generate_error_xml("Test error", "TEST_ERROR")
    assert "TEST_ERROR" in error_xml
    assert "Test error" in error_xml
    assert error_xml.strip().startswith("<")
    assert error_xml.strip().endswith(">")


@pytest.mark.asyncio
async def test_validation_service_init():
    """Test validation service initialization."""
    from app.services.validation_service import ValidationService
    
    service = ValidationService()
    assert hasattr(service, 'file_processor')
    assert hasattr(service, 'llm')


def test_sample_xml_validation():
    """Test XML validation with sample file."""
    from app.utils.xml_generator import XMLGenerator
    
    generator = XMLGenerator()
    
    # Create sample validation data
    sample_data = {
        "validation_id": "test-123",
        "status": "completed",
        "questions_file_id": "q-123",
        "report_file_id": "r-123",
        "total_questions": 2,
        "processed_questions": 2,
        "results": [
            {
                "question_id": "q1",
                "question": "Test question?",
                "answer": "Test answer",
                "confidence_score": 0.85,
                "relevant_sections": ["/test/path"],
                "status": "answered"
            }
        ],
        "validation_timestamp": "2024-01-01T12:00:00",
        "processing_time": 1.5
    }
    
    xml_output = generator.generate_validation_xml(sample_data)
    
    # Basic XML validation
    assert xml_output.strip().startswith("<")
    assert xml_output.strip().endswith(">")
    assert "test-123" in xml_output
    assert "Test question?" in xml_output
    assert "Test answer" in xml_output


def test_directory_creation():
    """Test that required directories are created."""
    from app.core.config import Settings
    
    # Create settings with temporary directories
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_settings = Settings(
            UPLOAD_PATH=os.path.join(temp_dir, "uploads"),
            PROCESSED_PATH=os.path.join(temp_dir, "processed")
        )
        
        # Directories should be created automatically
        assert os.path.exists(temp_settings.UPLOAD_PATH)
        assert os.path.exists(temp_settings.PROCESSED_PATH)


def test_app_creation():
    """Test FastAPI app creation."""
    from app.main import app
    
    assert app is not None
    assert hasattr(app, 'routes')
    
    # Check that basic routes exist
    route_paths = [route.path for route in app.routes]
    assert "/" in route_paths
    assert "/health" in route_paths


def test_system_ready_check():
    """Test system readiness check."""
    from app.core.config import settings
    
    # Test the system ready property
    assert hasattr(settings, 'is_system_ready')
    assert isinstance(settings.is_system_ready, bool)


if __name__ == "__main__":
    # Run basic tests
    print("Running basic tests...")
    
    test_imports()
    print("✓ Imports test passed")
    
    test_settings()
    print("✓ Settings test passed")
    
    test_xml_generator()
    print("✓ XML generator test passed")
    
    test_sample_xml_validation()
    print("✓ Sample XML validation test passed")
    
    test_directory_creation()
    print("✓ Directory creation test passed")
    
    test_app_creation()
    print("✓ App creation test passed")
    
    test_system_ready_check()
    print("✓ System ready check test passed")
    
    print("All basic tests passed!") 