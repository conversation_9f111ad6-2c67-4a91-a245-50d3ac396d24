#!/usr/bin/env python3
"""
XML Report Validation System - Startup Script (RAG-Enabled Version)

This script helps you set up and run the XML Report Validation System with RAG capabilities.
"""

import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """Check if Python version is supported."""
    if sys.version_info < (3, 8):
        print("ERROR: Python 3.8 or higher is required.")
        print(f"Current version: {sys.version}")
        return False
    print(f"Python version check passed: {sys.version}")
    return True


def check_dependencies():
    """Check if required dependencies are installed."""
    try:
        import fastapi
        import uvicorn
        import pandas
        import langchain_openai
        import chromadb
        print("Core dependencies are installed")
        return True
    except ImportError as e:
        print(f"ERROR: Missing dependencies: {e}")
        print("Please run: pip install -r requirements.txt")
        return False


def check_environment():
    """Check environment configuration."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("WARNING: .env file not found. Creating from .env.example...")
            env_file.write_text(env_example.read_text())
            print("Created .env file from template")
        else:
            print("WARNING: No .env file found. Creating basic configuration...")
            env_content = """# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Application Configuration
APP_NAME=XML Report Validation System
APP_VERSION=1.0.0
DEBUG=True
HOST=0.0.0.0
PORT=8000

# Local Data Paths
UPLOAD_PATH=./data/uploads
PROCESSED_PATH=./data/processed

# Vector Database Configuration
CHROMADB_MODE=embedded
CHROMADB_HOST=localhost
CHROMADB_PORT=8001
CHROMADB_PATH=./data/vector_db
VECTOR_DB_PATH=../VectorDB/embedded

# LangChain Configuration
MAX_TOKENS=4000
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# File Configuration
MAX_FILE_SIZE=10485760
ALLOWED_EXCEL_EXTENSIONS=[".xlsx", ".xls"]
ALLOWED_XML_EXTENSIONS=[".xml"]
"""
            env_file.write_text(env_content)
            print("Created basic .env file")
    
    # Check if OpenAI API key is set
    try:
        from dotenv import load_dotenv
        load_dotenv()
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key or api_key == "your_openai_api_key_here":
            print("WARNING: OpenAI API key not configured. Some features will be limited.")
            print("   Please set OPENAI_API_KEY in the .env file")
        else:
            print("OpenAI API key is configured")
    except ImportError:
        print("WARNING: python-dotenv not installed")
    
    return True


def check_directories():
    """Check if required directories exist."""
    directories = [
        "data/uploads",
        "data/processed",
        "data/vector_db"
    ]
    
    # Create local project directories
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("Required directories created/verified")
    return True


def setup_vector_database():
    """Set up vector database for RAG capabilities."""
    print("\nSetting up vector database for RAG...")
    
    try:
        # Check if vector database setup script exists
        if Path("verify_vectordb.py").exists():
            result = subprocess.run([sys.executable, "verify_vectordb.py"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("Vector database setup completed")
                return True
            else:
                print(f"Vector database setup warning: {result.stderr}")
                return False
        else:
            print("Vector database setup script not found")
            return False
    except Exception as e:
        print(f"Vector database setup warning: {e}")
        return False


def run_tests():
    """Run basic tests to verify setup."""
    print("\nRunning basic tests...")
    try:
        # Run the basic test file
        result = subprocess.run([sys.executable, "tests/test_basic.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("Basic tests passed")
            return True
        else:
            print(f"ERROR: Tests failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"WARNING: Could not run tests: {e}")
        return False


def start_server():
    """Start the FastAPI server."""
    print("\nStarting the XML Report Validation System with RAG...")
    print("Server will be available at: http://localhost:8000")
    print("API Documentation: http://localhost:8000/docs")
    print("Press Ctrl+C to stop the server")
    
    try:
        # Start uvicorn server
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "app.main:app",
            "--reload",
            "--host", "0.0.0.0",
            "--port", "8000"
        ])
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"ERROR: Error starting server: {e}")


def show_usage_info():
    """Show usage information."""
    print("\n📖 Quick Start Guide:")
    print("1. Upload questions: POST /api/v1/upload/questions")
    print("2. Upload XML report: POST /api/v1/upload/report")
    print("3. Validate report: POST /api/v1/validate")
    print("\nFor detailed API documentation, visit: http://localhost:8000/docs")
    print("Sample files are available in the docs/ directory")
    print("\nThis system uses RAG (Retrieval-Augmented Generation) with vector database")
    print("   for enhanced semantic search and context-aware validation")


def main():
    """Main startup function."""
    print("XML Report Validation System - RAG Setup & Startup")
    print("=" * 60)
    
    # Check system requirements
    if not check_python_version():
        return 1
    
    if not check_dependencies():
        print("\nTo install dependencies, run:")
        print("pip install -r requirements.txt")
        return 1
    
    # Setup environment
    check_environment()
    check_directories()
    
    # Setup vector database
    setup_vector_database()
    
    # Run tests
    run_tests()
    
    # Show usage info
    show_usage_info()
    
    # Ask user if they want to start the server
    # response = input("\n Start the server now? (y/n): ").lower().strip()
    # if response in ['y', 'yes']:
    #     start_server()
    # else:
    #     print("\nSetup completed!")
    #     print("To start the server later, run:")
    #     print("uvicorn app.main:app --reload")
    
    # return 0
    start_server()

if __name__ == "__main__":
    sys.exit(main()) 