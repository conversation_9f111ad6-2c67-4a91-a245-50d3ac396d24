{"validation_id": "8d8a75ea-41c0-4d90-be62-174510f91440", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181493", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "1c7e2b0d-e857-4a8a-ade1-72738128bde1", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "d985a8a6-a166-45bb-9feb-8dfebc4e031f", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "30ecca46-dfd5-429d-ae38-f36551708564", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name present but no comments or payment details provided.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/WaiverSection/Waiver"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "dddc8f51-a8ab-4ae1-8de6-f4e4b37ddadd", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name is missing; Client Specific Comments and Payment Section should not be filled.", "confidence_score": 0.98, "relevant_sections": ["Report/OrderDetails/ClientName", "Report/SpecialNotesSection/TextForSpecificClients", "Report/Payments/CreditOpinionNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6bcde13b-0652-4fae-a820-3aecb6978a8a", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Gross Profit and Total Income data not provided for compliance check.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "55f5660c-ebaa-473f-bcd0-bbd2f35d4fda", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Expenses for raw materials not found; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": [], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "7d7f7a6d-d2ce-4dba-8e15-eb33c677a2ca", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Requested and Company Name match; Client Specific Comments not populated.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/Requested", "Report/HeaderSection/CompanyName", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "39b9d2e1-27cf-4677-97ab-b9e5e436164a", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address does not match active address; Client Specific Comments not populated.", "confidence_score": 0.95, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ActiveAddress", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "6b81a47c-711c-4c69-ac67-1b097c4f9ca6", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not found in special notes; compliance violated.", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "95fffc20-af92-4414-83d7-6cad34bbea0c", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "Registration/license numbers are missing from required sections, suggest adding them.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/RegistrationNumbers", "Report/SpecialNotesSection/TextForSpecificClients"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "fb9a5d0d-929d-4230-b701-dad3025724d8", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in special notes section.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "a8908155-4553-461b-ab4f-5d6af936d17b", "question_number": 12, "question": "Spelling Check", "summary": "Spelling check shows 'asdasd' in Subsidiaries is non-compliant.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Subsidiaries"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "d8f831a5-b8a4-44b4-b91b-39f2f7dcd6ec", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No significant changes or adverse announcements found in the report.", "confidence_score": 0.9, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2659fdb9-d37d-4d5b-8a42-9260e17d52b3", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Validation failed: missing required sections for trade risk assessment.", "confidence_score": 0.9, "relevant_sections": ["Report/SignificantChanges", "Report/Payments"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "2b8bf432-1854-43f0-8bd5-5c8a30e97731", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found; compliance rule not applicable.", "confidence_score": 0.9, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "93a2f2d3-591a-420f-a9c7-57fb9d62bed0", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks relevant sector information from Subject activities.", "confidence_score": 0.98, "relevant_sections": ["Report/Activities/Subject Activities", "Report/Payments/Credit Opinion Notes"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "79a72836-64bb-449c-b8bb-ebc023d97d26", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Violation found: Incorrect texts added regarding export activities.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatusAdditional"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "200030ff-4355-4b77-9210-4fde895d9f40", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income are both missing; compliance rule violated.", "confidence_score": 0.95, "relevant_sections": ["Report/ProfitLossSection/Income", "Report/ProfitLossSection/TotalIncome"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "c9450ddf-69b0-48e5-b638-5ff380dc9a68", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Violation: No credit given and TRA high not indicated in Special Notes.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/LegalStatusSection/Capital/NotGiven"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6882b74c-5cbd-4f85-9e92-402f77b27fd3", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not found where applicable, violation detected.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ISO2CountryCode", "Report/AddressesSection/Addresses/Address/ICPCountryCode"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "49608343-00e9-4d8c-bcac-ab94be9f333e", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Company is active; no violation of dormant/ceased trading rule found.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus", "Report/AddressesSection/Addresses/Address/ActiveAddress"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "bb41b483-e0d0-4682-ba7a-05fb19c93786", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number not provided; compliance cannot be verified.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/LegalStatusSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "5d0570ec-5538-4064-944a-7298e6f8162e", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Registration number expired with no Comment found.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "76b55282-dd91-436b-ac5f-d8b9db02ca09", "question_number": 24, "question": "Small with large amount", "summary": "PaidUp capital of 123.00 violates the small with large amount rule.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "7dec8fd5-f87d-4978-a627-c8b35e892a56", "question_number": 25, "question": "Large with small amount", "summary": "PaidUp capital of 123.00 is small compared to the company's large status, violating the rule.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "02d6c778-666f-47ed-9f4f-93958776b0b2", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications are not present for compliance checking.", "confidence_score": 0.85, "relevant_sections": [], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9d69e7dc-5edd-4255-9eb4-7a19eb66455d", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Validation failed: Missing 'Research Type' as 'Negative' in <PERSON><PERSON>.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection", "Report/SpecialNotesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "ace327ec-f257-43cc-bdb6-3215a3596feb", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Validation failed: Research Type is 'Decline', Person Interviewed is missing.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/SpecialNotesSection/PersonInterviewed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "ed4987b1-ee18-4d5f-a76d-7ebfcf410cba", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Research type is missing; cannot verify compliance with decline rule.", "confidence_score": 0.85, "relevant_sections": ["Report/HeaderSection", "Report/PaymentsSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "bda0384a-c193-45ce-9d2c-cafda9290bfd", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Validation rejected: Missing 'Research Type' and 'Person Interviewed'.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/SpecialNotesSection/PersonInterviewed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "5b9f9469-17fc-4091-a228-a0dcc3f72a3c", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanctions search not performed; compliance rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5b7aea65-b76e-4db5-971f-9438a56bec47", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Sanctions not found; compliance rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/SanctionSearchPerformed", "Report/SanctionFound"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "7ad4997d-d3ad-470f-843e-13e064a63f7f", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "12dec341-8d25-476e-830a-6b51f231bd06", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in Header and LegalStatus sections.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "720c82c0-9207-4533-80c3-d91a3085f5be", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "No contact details present; violation of validation rule.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection/Addresses/Address"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "3fbecb32-adb9-429a-b306-083f1ddec5c8", "question_number": 36, "question": "Capital missing currency", "summary": "Capital currency field is blank; compliance rule violated.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5a160073-bff2-4619-9ed3-fab133fa35a2", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder not populated while being part of a related entity group.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/ParentCompany", "Report/LegalStatusSection/Subsidiaries"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "40a5a8d3-6558-4903-a35b-53827521bc8d", "question_number": 38, "question": "Calculate credit opinion", "summary": "Cannot verify credit opinion calculation; no payment data present.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "e62d8621-663e-42d6-8802-e242756d577b", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Compliance check passed; CompanyName matches Requested in financials.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "896a40a7-8887-4d56-906f-15626e076435", "question_number": 40, "question": "Town missing in town box", "summary": "Town box is empty; report cannot be sent for edit.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressesSection/Addresses/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "c504f53d-29fc-4c0e-ad62-0706437a37e1", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status is known as 'Active'; credit can be granted.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-11 16:26:56.194609", "processing_time": 13.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "ONLINEMISC", "rag_enabled": true, "permanent_question_bank_used": true}