# How Relevant Sections Detection Works

## Overview
The validation system uses a sophisticated multi-step process to detect and populate the `relevant_sections` field in validation results. This field shows exactly which XML paths were analyzed to generate each validation summary.

## Detection Process Flow

### 1. **XML Content Extraction** (`_extract_xml_content_for_question`)
The system first extracts relevant XML content for each validation question using two methods:

#### **LLM Semantic Search** (Primary - if enabled)
- **When**: `ENABLE_LLM_SEMANTIC_SEARCH = True` and LLM is available
- **Method**: `_llm_semantic_extract_xml_content()`
- **Intelligence**: Uses AI to understand question context and find semantically relevant sections

#### **Direct Extraction** (Fallback)
- **When**: LLM semantic search disabled or unavailable
- **Method**: `_direct_extract_xml_content()`
- **Method**: Simple extraction of first 10 XML sections

### 2. **Semantic Content Analysis** (`_llm_semantic_extract_xml_content`)

```python
# Configuration Settings (app/core/config.py)
ENABLE_LLM_SEMANTIC_SEARCH: bool = True
SEMANTIC_SEARCH_CONTENT_LIMIT: int = 12000      # Max content size before chunking
SEMANTIC_SEARCH_MAX_CHUNKS: int = 3             # Max chunks to process
SEMANTIC_SEARCH_CHUNK_SIZE: int = 8000          # Size of each chunk
SEMANTIC_SEARCH_MIN_EXTRACTION_SIZE: int = 2000 # Min extraction before processing more chunks
```

**Process Steps**:
1. **Content Preparation**: Combines XML structure and text content with paths
2. **Size Check**: If content > 12,000 characters, triggers advanced semantic extraction
3. **Semantic Matching**: Uses LLM to identify relevant sections based on question context

### 3. **Advanced Semantic Extraction** (`_llm_extract_relevant_sections`)

When content is large, the system uses intelligent chunking and extraction:

```python
extraction_prompt = f"""
You are an expert semantic content extraction assistant specializing in XML document analysis. 

QUESTION: {question}

ANALYSIS INSTRUCTIONS:
1. Identify XML sections that are directly relevant to answering the question
2. Look for semantic relationships, not just keyword matches
3. Include context sections that provide supporting information
4. Consider business logic and domain-specific meanings
5. Exclude irrelevant sections to keep the response concise
6. Preserve the original XML structure and paths
7. Focus on content that would help answer the specific question asked

SEMANTIC MATCHING PRIORITIES:
- Direct content matches (highest priority)
- Contextually related content
- Supporting information and metadata
- Business logic relationships
"""
```

**Multi-Chunk Processing**:
- Processes up to 3 chunks if initial extraction is insufficient
- Each additional chunk adds complementary information
- Preserves XML structure and context

### 4. **Relevant Sections Population** (LLM Response)

The LLM populates the `relevant_sections` array in its JSON response based on:

#### **Direct Path References**
```json
{
  "relevant_sections": [
    "Report/HeaderSection/CompanyName",
    "Report/HeaderSection/Requested",
    "Report/SpecialNotesSection/TransferToPrincipal"
  ]
}
```

#### **Semantic Understanding**
The LLM identifies which XML paths were actually used to make the validation decision:

**Example 1**: Company Name Matching
- **Question**: "Company name should match requested name"
- **Sections Used**: 
  - `"Report/HeaderSection/CompanyName"` (for company name)
  - `"Report/HeaderSection/Requested"` (for requested name)
  - `"Report/SpecialNotesSection/TransferToPrincipal"` (for comments check)

**Example 2**: Financial Validation
- **Question**: "Gross Profit should be less than Total Income"
- **Sections Used**: `[]` (empty - neither field found)

**Example 3**: Legal Status Check
- **Question**: "Spelling Check"
- **Sections Used**:
  - `"Report/LegalStatusSection/CompanyStatus"`
  - `"Report/LegalStatusSection/LegalStatus"`
  - `"Report/LegalStatusSection/DateStarted"`

## Real Examples from Validation Results

### **Example 1: Name Matching Validation**
```json
{
  "question": "Company name should match requested name",
  "summary": "Requested name 'GenAI25' matches official company name 'GenAI25'",
  "relevant_sections": [
    "Report/HeaderSection/Requested",
    "Report/HeaderSection/CompanyName",
    "Report/SpecialNotesSection/TransferToPrincipal"
  ]
}
```
**Analysis**: LLM checked both name fields and the special notes section for comments.

### **Example 2: Missing Data Validation**
```json
{
  "question": "Gross Profit should be less than Total Income",
  "summary": "Cannot verify: Neither Gross Profit nor Total Income found in XML",
  "relevant_sections": []
}
```
**Analysis**: LLM searched but found no relevant financial sections containing the required data.

### **Example 3: Address Validation**
```json
{
  "question": "Does address match active address?",
  "summary": "Client address does not match active address",
  "relevant_sections": [
    "Report/AddressesSection/Addresses/Address/ActiveAddress",
    "Report/SpecialNotesSection/TransferToPrincipal"
  ]
}
```
**Analysis**: LLM checked the address section and special notes for required comments.

## Technical Implementation

### **XML Path Generation**
The system maintains XML paths during processing:

```python
# From file_processor.py - XML text extraction
{
  "path": "Report/HeaderSection/CompanyName",
  "tag": "CompanyName", 
  "text": "GenAI25",
  "attributes": {}
}
```

### **Path Preservation**
During semantic extraction, the system preserves these paths so the LLM can reference them accurately in the `relevant_sections` array.

### **Intelligent Section Detection**
The LLM uses business logic understanding to identify:
- **Primary sections**: Direct data sources for validation
- **Secondary sections**: Supporting information and context  
- **Validation sections**: Areas where compliance results should be recorded

## Benefits of This Approach

1. **Transparency**: Shows exactly which XML sections were analyzed
2. **Traceability**: Enables auditing of validation decisions
3. **Accuracy**: Semantic understanding finds relevant sections beyond keyword matching
4. **Efficiency**: Chunking handles large XML documents intelligently
5. **Context Awareness**: Includes supporting sections for comprehensive validation

## Configuration Control

Administrators can control the detection process through settings:

```python
# Enable/disable semantic search
ENABLE_LLM_SEMANTIC_SEARCH = True

# Control content processing limits
SEMANTIC_SEARCH_CONTENT_LIMIT = 12000
SEMANTIC_SEARCH_CHUNK_SIZE = 8000
SEMANTIC_SEARCH_MAX_CHUNKS = 3
```

This multi-layered approach ensures that the `relevant_sections` field accurately reflects which XML paths were actually used to generate each validation result, providing full transparency into the validation process.

---

*Technical Documentation - Relevant Sections Detection*
*Last Updated: December 2024*
