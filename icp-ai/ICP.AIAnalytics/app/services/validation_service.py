from langchain_openai import Chat<PERSON>penAI
from langchain.chains import <PERSON><PERSON><PERSON><PERSON>
from langchain.prompts import PromptTemplate
from langchain.schema import SystemMessage, HumanMessage
from typing import List, Dict, Any, Optional
import asyncio
import json
import uuid
from datetime import datetime
import time
import xml.etree.ElementTree as ET

from app.core.config import settings
from app.models.schemas import ValidationResult, ValidationStatus, Question
from app.services.file_processor import FileProcessor


class ValidationService:
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            openai_api_key=settings.OPENAI_API_KEY,
            temperature=0,
            max_tokens=settings.MAX_TOKENS
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows
If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows
If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows the validation rule.

VALIDATION RULE: {question}

XML DATA:
{xml_content}

CRITICAL REQUIREMENTS:
1. Provide SPECIFIC findings with ACTUAL VALUES from the XML
2. If you cannot find relevant data, be explicit about what's missing
3. Use exact XML values in your summary

EXAMPLES:
❌ BAD: "Content available for review"
❌ BAD: "Information present in XML"
✅ GOOD: "Company name 'ABC Corp' matches requested 'ABC Corp'"
✅ GOOD: "Missing required field 'TaxNumber' in CompanySection"

RESPONSE FORMAT (JSON only):
{{
    "summary": "Specific validation finding with actual XML values (max 150 chars)",
    "confidence_score": 0.95,
    "relevant_sections": ["HeaderSection.CompanyName"],
    "status": "approved|rejected|manual_intervention_needed",
    "reasoning": "Detailed explanation of validation decision"
}}

CONFIDENCE SCORING:
- 0.9-1.0: Found exact data, clear validation result
- 0.7-0.8: Found relevant data, reasonable validation
- 0.3-0.6: Partial data, uncertain validation
- 0.1-0.2: Missing data, cannot validate

If you cannot find specific data to validate the rule, set confidence_score below 0.6 and status to "manual_intervention_needed".
"""
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template for better accuracy
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML compliance validator. Check if the XML data follows
