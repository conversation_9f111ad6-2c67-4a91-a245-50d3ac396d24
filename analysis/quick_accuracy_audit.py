def analyze_validation_patterns(validation_results):
    """Quick audit of existing validation accuracy issues"""
    
    issues = {
        "vague_summaries": [],
        "wrong_status_mapping": [],
        "low_confidence_approved": [],
        "generic_responses": []
    }
    
    for result in validation_results:
        # Issue 1: Vague summaries (major accuracy problem)
        if len(result.summary) < 30 or "available for review" in result.summary:
            issues["vague_summaries"].append(result)
        
        # Issue 2: High confidence but generic response
        if result.confidence_score > 0.8 and "XML content" in result.summary:
            issues["generic_responses"].append(result)
        
        # Issue 3: Low confidence but approved status
        if result.confidence_score < 0.6 and result.status == "approved":
            issues["low_confidence_approved"].append(result)
    
    return issues