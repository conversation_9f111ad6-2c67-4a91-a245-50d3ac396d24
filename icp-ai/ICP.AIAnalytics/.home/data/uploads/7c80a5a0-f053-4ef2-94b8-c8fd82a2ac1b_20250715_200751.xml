<Report xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <HeaderSection>
    <Date>29-Aug-2024</Date>
    <DeliveryDate>30-Aug-2024</DeliveryDate>
    <OurRef>998772.22.1</OurRef>
    <YourRef>Test22.1</YourRef>
    <CompanyName>AishwaryaTest</CompanyName>
  </HeaderSection>
  <PaymentsSection>
    <PaymentHistory>PaymentsNoTrace</PaymentHistory>
    <CreditOpinion>Large</CreditOpinion>
    <MaxCreditCurrency>AMD</MaxCreditCurrency>
    <MaxCredit>2321</MaxCredit>
    <OpinionOnMaxCredit>TermsStipulated</OpinionOnMaxCredit>
    <AdditionalNoteMaxCredit>test</AdditionalNoteMaxCredit>
    <TradeRiskAssessment>Average</TradeRiskAssessment>
    <CreditFigureCurrency>AMD</CreditFigureCurrency>
    <CreditFigure>2122</CreditFigure>
  </PaymentsSection>
  <FinancialSection>
    <BalanceSheetFigures>
      <AccountType>Consolidated (Subject &amp; Subsidiaries)</AccountType>
      <CompanyName>AishwaryaTest</CompanyName>
      <ScaleTheme>Units</ScaleTheme>
    </BalanceSheetFigures>
    <BalanceSheets>
      <BalanceSheet>
        <FiscalYear>2024</FiscalYear>
        <FiscalYearEndDate>01 January</FiscalYearEndDate>
        <InformationType>Complete</InformationType>
        <InformationLevel>Full</InformationLevel>
        <FinancialPeriodEndDate>01-Jan-2024</FinancialPeriodEndDate>
        <NumberOfMonths>1</NumberOfMonths>
        <Currency>AUD</Currency>
        <Scale>Units</Scale>
        <LineItems>
          <LineItem>
            <Level1>Assets</Level1>
            <Level2>Total Non-Current Assets</Level2>
            <Level3>Total Non-Current Assets</Level3>
            <Level4>Subtotal</Level4>
            <SourceDescription>Total Non-Current Assets</SourceDescription>
            <Amount>8268525.22</Amount>
            <KeyFinancialId>1</KeyFinancialId>
            <LineItemTypeId>2</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Assets</Level1>
            <Level2>Total Current Assets</Level2>
            <Level3>Total Current Assets</Level3>
            <Level4>Subtotal</Level4>
            <SourceDescription>Total Current Assets</SourceDescription>
            <Amount>79987.00</Amount>
            <KeyFinancialId>2</KeyFinancialId>
            <LineItemTypeId>2</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Liabilities</Level1>
            <Level2>Total Liabilities</Level2>
            <Level3>Total Liabilities</Level3>
            <Level4>Total</Level4>
            <SourceDescription>Total Liabilities</SourceDescription>
            <Amount>44000.00</Amount>
            <KeyFinancialId>6</KeyFinancialId>
            <LineItemTypeId>3</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Total Assets less Liabilities</Level1>
            <Level2>Total Assets less Liabilities</Level2>
            <Level3>Total Assets less Liabilities</Level3>
            <Level4>Total</Level4>
            <SourceDescription>Total Assets less Liabilities</SourceDescription>
            <Amount>8304512.22</Amount>
            <KeyFinancialId>7</KeyFinancialId>
            <LineItemTypeId>3</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Liabilities</Level1>
            <Level2>Total Current Liabilities</Level2>
            <Level3>Total Current Liabilities</Level3>
            <Level4>Subtotal</Level4>
            <SourceDescription>Total Current Liabilities</SourceDescription>
            <Amount>4000.00</Amount>
            <KeyFinancialId>4</KeyFinancialId>
            <LineItemTypeId>2</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Assets</Level1>
            <Level2>Total Assets</Level2>
            <Level3>Total Assets</Level3>
            <Level4>Total</Level4>
            <SourceDescription>Total Assets</SourceDescription>
            <Amount>8348512.22</Amount>
            <KeyFinancialId>3</KeyFinancialId>
            <LineItemTypeId>3</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Liabilities</Level1>
            <Level2>Total Non-Current Liabilities</Level2>
            <Level3>Total Non-Current Liabilities</Level3>
            <Level4>Subtotal</Level4>
            <SourceDescription>Total Non-Current Liabilities</SourceDescription>
            <Amount>40000.00</Amount>
            <KeyFinancialId>5</KeyFinancialId>
            <LineItemTypeId>2</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Assets</Level1>
            <Level2>Non Current Assets</Level2>
            <Level3>Fixed Assets</Level3>
            <Level4>Line Item</Level4>
            <SourceDescription>testt</SourceDescription>
            <Amount>5000.00</Amount>
            <CategoryId>1</CategoryId>
            <LineItemTypeId>1</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Assets</Level1>
            <Level2>Non Current Assets</Level2>
            <Level3>Deferred Tax Asset</Level3>
            <Level4>Line Item</Level4>
            <SourceDescription>test</SourceDescription>
            <Amount>-675.55</Amount>
            <CategoryId>1</CategoryId>
            <LineItemTypeId>1</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Assets</Level1>
            <Level2>Non Current Assets</Level2>
            <Level3>Goodwill</Level3>
            <Level4>Line Item</Level4>
            <SourceDescription>12</SourceDescription>
            <Amount>-98.23</Amount>
            <CategoryId>1</CategoryId>
            <LineItemTypeId>1</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Assets</Level1>
            <Level2>Non Current Assets</Level2>
            <Level3>Long Term Debtors</Level3>
            <Level4>Line Item</Level4>
            <SourceDescription>test3</SourceDescription>
            <Amount>454343.00</Amount>
            <CategoryId>1</CategoryId>
            <LineItemTypeId>1</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Assets</Level1>
            <Level2>Non Current Assets</Level2>
            <Level3>Other Non-Current Assets</Level3>
            <Level4>Line Item</Level4>
            <SourceDescription>tes</SourceDescription>
            <Amount>7809956.00</Amount>
            <CategoryId>1</CategoryId>
            <LineItemTypeId>1</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Assets</Level1>
            <Level2>Current Assets</Level2>
            <Level3>Short Term Non-Financial Investments</Level3>
            <Level4>Line Item</Level4>
            <SourceDescription>test</SourceDescription>
            <Amount>40000.00</Amount>
            <CategoryId>2</CategoryId>
            <LineItemTypeId>1</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Assets</Level1>
            <Level2>Current Assets</Level2>
            <Level3>Cash &amp; Bank - Asset</Level3>
            <Level4>Line Item</Level4>
            <SourceDescription>13</SourceDescription>
            <Amount>-234.00</Amount>
            <CategoryId>2</CategoryId>
            <LineItemTypeId>1</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Assets</Level1>
            <Level2>Current Assets</Level2>
            <Level3>Inventories</Level3>
            <Level4>Line Item</Level4>
            <SourceDescription>123</SourceDescription>
            <Amount>123.00</Amount>
            <CategoryId>2</CategoryId>
            <LineItemTypeId>1</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Assets</Level1>
            <Level2>Current Assets</Level2>
            <Level3>Other Current Assets</Level3>
            <Level4>Line Item</Level4>
            <SourceDescription>12</SourceDescription>
            <Amount>40098.00</Amount>
            <CategoryId>2</CategoryId>
            <LineItemTypeId>1</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Liabilities</Level1>
            <Level2>Current Liabilities</Level2>
            <Level3>Other Liabilities</Level3>
            <Level4>Line Item</Level4>
            <SourceDescription>test</SourceDescription>
            <Amount>4000.00</Amount>
            <CategoryId>3</CategoryId>
            <LineItemTypeId>1</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Liabilities</Level1>
            <Level2>Non-Current Liabilities</Level2>
            <Level3>Deferred Tax Liability</Level3>
            <Level4>Line Item</Level4>
            <SourceDescription>test </SourceDescription>
            <Amount>40000.00</Amount>
            <CategoryId>4</CategoryId>
            <LineItemTypeId>1</LineItemTypeId>
          </LineItem>
        </LineItems>
      </BalanceSheet>
    </BalanceSheets>
    <ProfitAndLossFigures>
      <AccountType>Subject</AccountType>
      <CompanyName>AishwaryaTest</CompanyName>
      <ScaleTheme>Units</ScaleTheme>
    </ProfitAndLossFigures>
    <ProfitAndLossAccts>
      <ProfitAndLossAcct>
        <FiscalYear>2022</FiscalYear>
        <FiscalYearEndDate>01 January</FiscalYearEndDate>
        <InformationType>Extract</InformationType>
        <InformationLevel>Full</InformationLevel>
        <FinancialPeriodEndDate>01-Jan-2022</FinancialPeriodEndDate>
        <NumberOfMonths>2</NumberOfMonths>
        <Currency>ANG</Currency>
        <Scale>Units</Scale>
        <LineItems>
          <LineItem>
            <Level1>Profit After Tax</Level1>
            <Level2>Total Net Profit</Level2>
            <Level3>Net Profit</Level3>
            <Level4>Subtotal</Level4>
            <SourceDescription>Net Profit</SourceDescription>
            <Amount>67.00</Amount>
            <KeyFinancialId>15</KeyFinancialId>
            <LineItemTypeId>2</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Income</Level1>
            <Level2>Total Income</Level2>
            <Level3>Total Income</Level3>
            <Level4>Subtotal</Level4>
            <SourceDescription>Total Income</SourceDescription>
            <Amount>67000.00</Amount>
            <KeyFinancialId>9</KeyFinancialId>
            <LineItemTypeId>2</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Gross Profit</Level1>
            <Level2>Gross Profit</Level2>
            <Level3>Gross Profit</Level3>
            <Level4>Subtotal</Level4>
            <SourceDescription>Gross Profit</SourceDescription>
            <Amount>67.00</Amount>
            <KeyFinancialId>11</KeyFinancialId>
            <LineItemTypeId>2</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Operating Profit</Level1>
            <Level2>Total Operating Profit</Level2>
            <Level3>Total Operating Profit</Level3>
            <Level4>Subtotal</Level4>
            <SourceDescription>Total Operating Profit</SourceDescription>
            <Amount>67.00</Amount>
            <KeyFinancialId>13</KeyFinancialId>
            <LineItemTypeId>2</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Net P&amp;L</Level1>
            <Level2>Total Profit Before Tax</Level2>
            <Level3>Profit Before Tax</Level3>
            <Level4>Subtotal</Level4>
            <SourceDescription>Profit Before Tax</SourceDescription>
            <Amount>67.00</Amount>
            <KeyFinancialId>14</KeyFinancialId>
            <LineItemTypeId>2</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Adjusted P&amp;L</Level1>
            <Level2>Total Adjusted P&amp;L</Level2>
            <Level3>Total Adjusted Profit</Level3>
            <Level4>Total</Level4>
            <SourceDescription>Total Adjusted Profit</SourceDescription>
            <Amount>67.00</Amount>
            <KeyFinancialId>16</KeyFinancialId>
            <LineItemTypeId>3</LineItemTypeId>
          </LineItem>
          <LineItem>
            <Level1>Income</Level1>
            <Level2>Income</Level2>
            <Level3>Income</Level3>
            <Level4>Line Item</Level4>
            <SourceDescription>test</SourceDescription>
            <Amount>67.00</Amount>
            <CategoryId>6</CategoryId>
            <LineItemTypeId>1</LineItemTypeId>
          </LineItem>
        </LineItems>
      </ProfitAndLossAcct>
      <ProfitAndLossAcct>
        <FiscalYear>2023</FiscalYear>
        <FiscalYearEndDate>01 January</FiscalYearEndDate>
        <InformationType>Complete</InformationType>
        <InformationLevel>Full</InformationLevel>
        <FinancialPeriodEndDate>01-Jan-2023</FinancialPeriodEndDate>
        <NumberOfMonths>2</NumberOfMonths>
        <Currency>ANG</Currency>
        <Scale>Units</Scale>
      </ProfitAndLossAcct>
    </ProfitAndLossAccts>
  </FinancialSection>
  <LegalStatusSection>
    <DateStarted>08-Mar-2024</DateStarted>
    <CompanyStatus>Active</CompanyStatus>
    <Capital>
      <NotGiven>false</NotGiven>
    </Capital>
    <ICPLegalGroup>Public Limited Company</ICPLegalGroup>
    <LegalStatusCode>2000</LegalStatusCode>
    <LegalStatus>Public Limited Liability Company</LegalStatus>
  </LegalStatusSection>
  <RelatedEntitiesSection>
    <RelatedEntity>
      <Relationship>Associate</Relationship>
      <CompanyName>Aish89</CompanyName>
      <CSRNumber>999002</CSRNumber>
      <RelatedEntitiesAddressSection>
        <Address>
          <ActiveAddress>true</ActiveAddress>
          <DateOccupiedFrom>01-Jul-2024</DateOccupiedFrom>
          <DateOccupiedTo>31-Jul-2024</DateOccupiedTo>
          <CareOf>1.1</CareOf>
          <Building>1.1</Building>
          <BuildingCommercialName>1.1</BuildingCommercialName>
          <Street>1.1</Street>
          <POBox>1.1</POBox>
          <Town>1.124</Town>
          <ProvinceType>Region</ProvinceType>
          <ISO2CountryCode>AL</ISO2CountryCode>
          <ICPCountryCode>ALB</ICPCountryCode>
          <ICPCountryName>Albania</ICPCountryName>
          <GPSLocation>1.1</GPSLocation>
        </Address>
        <Telephone>
          <InternationalDialingCode>355</InternationalDialingCode>
          <Number>1.1</Number>
          <Extension>1.1</Extension>
        </Telephone>
      </RelatedEntitiesAddressSection>
      <RelatedEntitiesTradingStyles>
        <TradingStyle>
          <Type>Trading Style</Type>
          <LocalLanguage>1.1</LocalLanguage>
          <LocalName>1.1</LocalName>
          <EnglishName>1.1</EnglishName>
        </TradingStyle>
      </RelatedEntitiesTradingStyles>
      <RelatedEntitiesLegalStatusSection>
        <DateStarted>2024</DateStarted>
        <CompanyStatus>Cancelled</CompanyStatus>
        <History>1.1</History>
        <RegistrationNumbers>
          <RegistrationNumber>
            <ICPRegistrationNumberName>Tax Number</ICPRegistrationNumberName>
            <LocalRegistrationNumberName>Tax Numbe</LocalRegistrationNumberName>
            <LocalAbbreviation>TN</LocalAbbreviation>
            <IssuingAuthority>AQ</IssuingAuthority>
            <RegistrationNumberValue>1.1</RegistrationNumberValue>
            <ICPRegistrationNumberTypeId>27</ICPRegistrationNumberTypeId>
          </RegistrationNumber>
        </RegistrationNumbers>
        <ICPLegalGroup>Government</ICPLegalGroup>
        <LegalStatusCode>2088</LegalStatusCode>
        <LegalStatus>Government Agency</LegalStatus>
      </RelatedEntitiesLegalStatusSection>
    </RelatedEntity>
    <RelatedEntity>
      <Relationship>Subsidiary</Relationship>
      <CompanyName>check Existing process</CompanyName>
      <CSRNumber>998702</CSRNumber>
      <RelatedEntitiesLegalStatusSection>
        <DateStarted>2023</DateStarted>
        <CompanyStatus>Active</CompanyStatus>
        <History>test</History>
        <ICPLegalGroup>Unlimited Partnership</ICPLegalGroup>
        <LegalStatusCode>3000</LegalStatusCode>
        <LegalStatus>General Partnership</LegalStatus>
      </RelatedEntitiesLegalStatusSection>
    </RelatedEntity>
    <RelatedEntity>
      <Relationship>Subsidiary</Relationship>
      <CompanyName>check2</CompanyName>
      <CSRNumber>999077</CSRNumber>
      <RelatedEntitiesAddressSection>
        <Address>
          <ActiveAddress>true</ActiveAddress>
          <Building>checky</Building>
          <Street>check</Street>
          <Town>checek</Town>
          <ProvinceType>Province</ProvinceType>
          <ISO2CountryCode>AF</ISO2CountryCode>
          <ICPCountryCode>AFG</ICPCountryCode>
          <ICPCountryName>Afghanistan</ICPCountryName>
        </Address>
      </RelatedEntitiesAddressSection>
    </RelatedEntity>
    <RelatedEntity>
      <Relationship>Subsidiary</Relationship>
      <CompanyName>test44</CompanyName>
      <CSRNumber>999158</CSRNumber>
      <RelatedEntitiesAddressSection>
        <Address>
          <ActiveAddress>true</ActiveAddress>
          <Building>test</Building>
          <Street>test</Street>
          <Town>test44</Town>
          <ProvinceType>Region</ProvinceType>
          <ISO2CountryCode>AL</ISO2CountryCode>
          <ICPCountryCode>ALB</ICPCountryCode>
          <ICPCountryName>Albania</ICPCountryName>
        </Address>
      </RelatedEntitiesAddressSection>
    </RelatedEntity>
    <RelatedEntity>
      <Relationship>Associate</Relationship>
      <CompanyName>test78</CompanyName>
      <CSRNumber>999081</CSRNumber>
      <RelatedEntitiesAddressSection>
        <Address>
          <ActiveAddress>true</ActiveAddress>
          <Town>etes</Town>
          <ProvinceType>Region</ProvinceType>
          <ISO2CountryCode>AL</ISO2CountryCode>
          <ICPCountryCode>ALB</ICPCountryCode>
          <ICPCountryName>Albania</ICPCountryName>
        </Address>
      </RelatedEntitiesAddressSection>
    </RelatedEntity>
    <RelatedEntity>
      <Relationship>Associate</Relationship>
      <CompanyName>test89</CompanyName>
      <CSRNumber>999078</CSRNumber>
      <RelatedEntitiesAddressSection>
        <Address>
          <ActiveAddress>true</ActiveAddress>
          <Building>test</Building>
          <Street>test</Street>
          <Town>test</Town>
          <ISO2CountryCode>PG</ISO2CountryCode>
          <ICPCountryCode>ADM</ICPCountryCode>
          <ICPCountryName>Admiralty Islands</ICPCountryName>
        </Address>
      </RelatedEntitiesAddressSection>
    </RelatedEntity>
    <RelatedEntity>
      <Relationship>Affiliate</Relationship>
      <CompanyName>test9</CompanyName>
      <CSRNumber>999076</CSRNumber>
      <RelatedEntitiesAddressSection>
        <Address>
          <ActiveAddress>true</ActiveAddress>
          <Building>test</Building>
          <Street>test</Street>
          <Town>test</Town>
          <ISO2CountryCode>PG</ISO2CountryCode>
          <ICPCountryCode>ADM</ICPCountryCode>
          <ICPCountryName>Admiralty Islands</ICPCountryName>
        </Address>
      </RelatedEntitiesAddressSection>
    </RelatedEntity>
    <RelatedEntity>
      <Relationship>Subsidiary</Relationship>
      <CompanyName>test900</CompanyName>
      <CSRNumber>999083</CSRNumber>
      <RelatedEntitiesAddressSection>
        <Address>
          <ActiveAddress>true</ActiveAddress>
          <ProvinceType>Province</ProvinceType>
          <ISO2CountryCode>AF</ISO2CountryCode>
          <ICPCountryCode>AFG</ICPCountryCode>
          <ICPCountryName>Afghanistan</ICPCountryName>
        </Address>
      </RelatedEntitiesAddressSection>
    </RelatedEntity>
    <RelatedEntity>
      <Relationship>Affiliate</Relationship>
      <CompanyName>TestCompanyB!</CompanyName>
      <CSRNumber>998791</CSRNumber>
      <RelatedEntitiesAddressSection>
        <Address>
          <ActiveAddress>true</ActiveAddress>
          <DateOccupiedFrom>14-Apr-2024</DateOccupiedFrom>
          <DateOccupiedTo>24-Aug-2024</DateOccupiedTo>
          <Building>AQ</Building>
          <BuildingCommercialName>Xr</BuildingCommercialName>
          <PostCode>232342</PostCode>
          <ISO2CountryCode>IN</ISO2CountryCode>
          <ICPCountryCode>IND</ICPCountryCode>
          <ICPCountryName>India</ICPCountryName>
          <OccupancyType>Own Freehold</OccupancyType>
        </Address>
        <Telephone>
          <InternationalDialingCode>91</InternationalDialingCode>
          <Number>7656567876</Number>
          <Type>Mobile</Type>
        </Telephone>
      </RelatedEntitiesAddressSection>
      <RelatedEntitiesTradingStyles>
        <TradingStyle>
          <Type>Formerly Known</Type>
          <EnglishName>Test</EnglishName>
        </TradingStyle>
      </RelatedEntitiesTradingStyles>
      <RelatedEntitiesLegalStatusSection>
        <DateStarted>15-Apr-2024</DateStarted>
        <CompanyStatus>Active</CompanyStatus>
        <RegistrationNumbers>
          <RegistrationNumber>
            <ICPRegistrationNumberName>Business Registration Number</ICPRegistrationNumberName>
            <LocalRegistrationNumberName>test</LocalRegistrationNumberName>
            <RegistrationNumberValue>234232</RegistrationNumberValue>
            <DateIssued>15-Apr-2024</DateIssued>
            <DateExpired>24-Oct-2025</DateExpired>
            <RenewalFrequency>Quarterly</RenewalFrequency>
            <ICPRegistrationNumberTypeId>4</ICPRegistrationNumberTypeId>
          </RegistrationNumber>
        </RegistrationNumbers>
        <ICPLegalGroup>Public Limited Company</ICPLegalGroup>
        <LegalStatusCode>2000</LegalStatusCode>
        <LegalStatus>Public Limited Liability Company</LegalStatus>
      </RelatedEntitiesLegalStatusSection>
    </RelatedEntity>
    <RelatedEntity>
      <Relationship>Affiliate</Relationship>
      <CompanyName>TestCompanyRe444</CompanyName>
      <CSRNumber>998805</CSRNumber>
      <RelatedEntitiesTradingStyles>
        <TradingStyle>
          <Type>Short Form</Type>
          <EnglishName>testtrad</EnglishName>
        </TradingStyle>
      </RelatedEntitiesTradingStyles>
      <RelatedEntitiesLegalStatusSection>
        <DateStarted>17-Feb-2024</DateStarted>
        <CompanyStatus>Active</CompanyStatus>
        <RegistrationNumbers>
          <RegistrationNumber>
            <ICPRegistrationNumberName>Other Registration Number2</ICPRegistrationNumberName>
            <LocalRegistrationNumberName>COCM</LocalRegistrationNumberName>
            <LocalAbbreviation>CO ab</LocalAbbreviation>
            <RegistrationNumberValue>2324234</RegistrationNumberValue>
            <RenewalFrequency>Bi Annually</RenewalFrequency>
            <ICPRegistrationNumberTypeId>2</ICPRegistrationNumberTypeId>
          </RegistrationNumber>
        </RegistrationNumbers>
        <ICPLegalGroup>Civil Company/Partnership - Non-Commercial</ICPLegalGroup>
        <LegalStatusCode>3006</LegalStatusCode>
        <LegalStatus>Civil Partnership</LegalStatus>
      </RelatedEntitiesLegalStatusSection>
    </RelatedEntity>
    <RelatedEntity>
      <Relationship>Affiliate</Relationship>
      <CompanyName>testCompanyrel22</CompanyName>
      <CSRNumber>998802</CSRNumber>
      <RelatedEntitiesAddressSection>
        <Telephone>
          <InternationalDialingCode>91</InternationalDialingCode>
          <Number>567656</Number>
          <Type>Mobile</Type>
        </Telephone>
      </RelatedEntitiesAddressSection>
      <RelatedEntitiesTradingStyles>
        <TradingStyle>
          <Type>Trading Style</Type>
          <EnglishName>testcomrel22</EnglishName>
        </TradingStyle>
      </RelatedEntitiesTradingStyles>
      <RelatedEntitiesLegalStatusSection>
        <DateStarted>10-Jan-2024</DateStarted>
        <CompanyStatus>Active</CompanyStatus>
        <RegistrationNumbers>
          <RegistrationNumber>
            <ICPRegistrationNumberName>Other Registration Number3</ICPRegistrationNumberName>
            <LocalRegistrationNumberName>BN17Sep</LocalRegistrationNumberName>
            <RegistrationNumberValue>343565</RegistrationNumberValue>
            <DateIssued>07-Feb-2024</DateIssued>
            <DateExpired>28-Apr-2024</DateExpired>
            <RenewalFrequency>Quarterly</RenewalFrequency>
            <ICPRegistrationNumberTypeId>3</ICPRegistrationNumberTypeId>
          </RegistrationNumber>
        </RegistrationNumbers>
        <ICPLegalGroup>Public Limited Company</ICPLegalGroup>
        <LegalStatusCode>7600</LegalStatusCode>
        <LegalStatus>Bank</LegalStatus>
      </RelatedEntitiesLegalStatusSection>
    </RelatedEntity>
    <RelatedEntity>
      <Relationship>Affiliate</Relationship>
      <CompanyName>testing</CompanyName>
      <CSRNumber>999072</CSRNumber>
      <RelatedEntitiesAddressSection>
        <Address>
          <ActiveAddress>true</ActiveAddress>
          <Building>test</Building>
          <Street>test</Street>
          <Town>test</Town>
          <ISO2CountryCode>PG</ISO2CountryCode>
          <ICPCountryCode>ADM</ICPCountryCode>
          <ICPCountryName>Admiralty Islands</ICPCountryName>
        </Address>
      </RelatedEntitiesAddressSection>
    </RelatedEntity>
    <RelatedEntity>
      <Relationship>Affiliate</Relationship>
      <CompanyName>yest</CompanyName>
      <CSRNumber>999088</CSRNumber>
      <RelatedEntitiesAddressSection>
        <Address>
          <ActiveAddress>true</ActiveAddress>
          <ProvinceType>Region</ProvinceType>
          <ISO2CountryCode>AL</ISO2CountryCode>
          <ICPCountryCode>ALB</ICPCountryCode>
          <ICPCountryName>Albania</ICPCountryName>
        </Address>
      </RelatedEntitiesAddressSection>
    </RelatedEntity>
  </RelatedEntitiesSection>
  <SpecialNotesSection>
    <TransferToPrincipal>false</TransferToPrincipal>
  </SpecialNotesSection>
  <WaiverSection>
    <Waiver>All information within this report is provided entirely on an 'as is' basis and is used by the recipient at its sole risk. ICP shall use reasonable skill and care in providing information contained within this report and, specifically, in collecting and collating the information from reliable sources and will have no legal responsibility for any error or omission contained within this report or subsequent change to the information. It does not give any warranty or guarantee to information provided or consequences of using the information within this report for decision making purposes.

Information is to be stored and used in accordance with data protection regulation and will not be supplied, published, displayed, re-sold, copied or made known to the subject without prior agreement from ICP.</Waiver>
  </WaiverSection>
</Report>