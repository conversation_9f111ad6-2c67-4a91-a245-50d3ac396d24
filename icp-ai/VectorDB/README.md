# ChromaDB Vector Database Storage

This directory contains the ChromaDB vector database files for the XML Report Validation System.

## About

This directory is located **outside** the main project folder (`ICP.AIAnalytics`) to:

1. **Separate data from code** - Keep vector embeddings separate from application code
2. **Improve storage management** - Easier to backup, migrate, or scale vector data
3. **Reduce project size** - Vector databases can become large over time
4. **Better deployment flexibility** - Can be mounted separately in containerized deployments

## Directory Structure

```
VectorDB/
├── README.md           # This file
├── .gitkeep           # Git placeholder
├── chroma.sqlite3     # ChromaDB database (created automatically)
├── docker/            # Docker volume mount point
└── [collections]/     # Individual collection data (created automatically)
```

## Configuration

The application is configured to use this external directory via:
- `CHROMADB_PATH=../VectorDB` in `app/core/config.py`
- `VECTOR_DB_PATH=../VectorDB` for backward compatibility

## Usage

This directory is automatically created and managed by the application. No manual intervention is required.

When running the application:
1. ChromaDB will automatically create necessary files here
2. Vector embeddings from XML reports and questions are stored here
3. The application will create collections as needed

## Docker Deployment

For Docker deployments, this directory is mounted as:
```yaml
volumes:
  - ../VectorDB/docker:/chroma/chroma
```

## Backup Recommendations

To backup your vector data:
```bash
# Backup the entire VectorDB directory
tar -czf vectordb_backup_$(date +%Y%m%d).tar.gz ../VectorDB/

# Or use rsync for incremental backups
rsync -av ../VectorDB/ /path/to/backup/location/
```

## Maintenance

The application includes automatic cleanup features:
- Old collections are automatically removed based on retention settings
- Storage optimization is performed periodically
- Compression is applied to reduce storage usage

For manual maintenance, use the API endpoints:
- `GET /api/v1/storage/stats` - View storage statistics
- `POST /api/v1/storage/cleanup` - Trigger cleanup
- `POST /api/v1/storage/compress` - Compress old data 