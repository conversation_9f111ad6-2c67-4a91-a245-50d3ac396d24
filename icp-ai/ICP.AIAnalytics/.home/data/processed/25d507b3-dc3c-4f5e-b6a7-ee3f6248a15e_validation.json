{"validation_id": "25d507b3-dc3c-4f5e-b6a7-ee3f6248a15e", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181493", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "383387ec-0bac-4fd1-ba4d-bec5d5ded8ef", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "********-9b25-4864-89d8-66936e3d8270", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "d87726ca-fb94-4151-8b18-45f22989588b", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name 'GenAI25' present but Client Specific Comments and Payment Section empty.", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "fdf37f29-eed8-4a91-8d66-23f17e35e210", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name field is missing; Client Specific Comments and Payment Section are empty.", "confidence_score": 0.98, "relevant_sections": ["Report/OrderDetails/ClientName", "Report/SpecialNotesSection/TextForSpecificClients", "Report/Payments/CreditOpinionNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "504d6385-5656-48b5-9e51-603bfdef8374", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Gross Profit and Total Income values not found, cannot verify compliance.", "confidence_score": 0.85, "relevant_sections": ["Report/FinancialSection/GrossProfit", "Report/FinancialSection/TotalIncome"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "26569632-50d4-49d8-9b7a-88326c3d84db", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "No expenses for raw materials found; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": ["Report/Financial/ProfitAndLoss"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "4f0aa174-7714-441a-b0fa-4222090fe679", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Company name 'GenAI25' matches requested name 'GenAI25'.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f39c06c4-9e61-451d-9ab8-1694d55c1eb2", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address not active; Client Specific Comments not populated.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ActiveAddress", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "87bc5ab1-e485-4475-9ba3-fa1b663559aa", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not specified in special notes, violates compliance rule.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "fc10793c-c0e9-4dbd-b76e-9cc9ac314aed", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "No License or Registration numbers found in report, missing required information.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/RegistrationNumbers", "Report/SpecialNotesSection/Text for Specific Clients"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "5f42ad79-87b6-44fe-8243-969e619402f3", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in special notes section.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "80cf3d91-6957-4353-9a48-4f824657652e", "question_number": 12, "question": "Spelling Check", "summary": "No spelling errors detected in report content.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested", "Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatus", "Report/WaiverSection/Waiver"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "ef008290-32fe-46bd-82b8-e5a15f69fc2c", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "No adverse announcements found in significant changes or payments.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection", "Report/WaiverSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "b00c7ed3-d292-4513-b64e-6c158610bb35", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "No adverse announcements in significant changes or payments sections, violates validation rule.", "confidence_score": 0.9, "relevant_sections": ["Report/SignificantChangesSection", "Report/PaymentsSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "15244115-8f54-4fae-a025-1f8e964a1676", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": ["Report/SpecialNotesSection", "Report/WaiverSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "a8d6b52f-edb6-44d9-8ba3-63fab4331edd", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks sector-related info for company 'GenAI25'.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "818eb7ad-af35-49c8-908e-4f83af987ce7", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "No incorrect texts found in service activities section.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9178f5a5-1108-4ba1-a1b6-fcd9e6243d97", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income are both absent, cannot verify compliance.", "confidence_score": 0.9, "relevant_sections": ["Report/ProfitAndLoss/Income", "Report/ProfitAndLoss/TotalIncome"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "cf98ba41-3fda-492c-99e9-c6d48fa1dee7", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Credit opinion currency not specified, TRA high condition unmet.", "confidence_score": 0.85, "relevant_sections": ["Report/SpecialNotesSection", "Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "0af0e375-dc67-4e5e-9552-8e5e0f20f6df", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not applicable, no comment link provided.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ISO2CountryCode"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "303d7e16-47b1-4b49-a8e5-f325c9ca23f6", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Company status is 'Active', no violation for dormant/ceased trading.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "fb5e6c11-0374-44ec-8d2e-f9e6885e5178", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number not provided; cannot verify compliance with branch requirement.", "confidence_score": 0.85, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/LegalStatusSection/ParentCompany"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "60420131-9f34-4dce-bf52-0f34ef3324bf", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "No registration number found; cannot verify expiry comment requirement.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "155a49fb-ada0-4092-8b98-5fd9070e4259", "question_number": 24, "question": "Small with large amount", "summary": "PaidUp capital '123.00' is small compared to large expected amount, violates rule.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "dc05cfb2-0d79-4526-88dd-2a43c58ecd88", "question_number": 25, "question": "Large with small amount", "summary": "PaidUp capital '123.00' is large, but '1' in YourRef is small, violates rule.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/HeaderSection/YourRef"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "cb7c24ec-1cd6-434d-becf-2c5d82094ed2", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications not provided for validation, cannot verify compliance.", "confidence_score": 0.85, "relevant_sections": ["Report/Personnel/Position", "Report/Personnel/PositionClassifications"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "25dc1005-7f6c-4869-93af-0abaa480b300", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Header and Address sections present, but LegalStatus and Waiver sections violate the negative report requirement.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection", "Report/LegalStatusSection", "Report/WaiverSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "81a14bc2-e7b2-4e47-8e59-63f3456f0fc5", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Research Type is 'Decline', Person Interviewed is missing, compliant.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/SpecialNotesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "7c297435-5ec5-4337-a6b9-28567e2dffc1", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Research type is missing; cannot verify compliance with decline rule.", "confidence_score": 0.85, "relevant_sections": ["Report/HeaderSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "606c5f7c-359f-48b1-92d9-b7a884e0b8bb", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Research Type is blank, but no comments or Person Interviewed present, violates compliance.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/PaymentsSection/CreditOpinionNotes", "Report/SpecialNotesSection/PersonInterviewed"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "268a5655-a70c-4b20-90b0-9b330b7d30e3", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanctions search not performed; missing required tag.", "confidence_score": 0.98, "relevant_sections": ["Report/SanctionSection/SanctionSearchPerformed"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "fcf58d7d-6c67-4411-9d92-40e781466e9b", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "No sanctions found; compliance rule violated.", "confidence_score": 0.9, "relevant_sections": ["Report/SanctionSection/SanctionSearchPerformed", "Report/SanctionSection/SanctionFound"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "57813033-df71-4ab6-8ee3-9170b684f247", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "78908647-9ab9-4c66-934d-5138679da938", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in Header and LegalStatus sections.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "56858486-e68c-4b82-94ef-15b4e5c3e9e6", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Research type is 'Select' but no contact details provided.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/AddressesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "393f082f-ecb8-460c-9b56-105c534bfbac", "question_number": 36, "question": "Capital missing currency", "summary": "Capital currency field is missing; 'Currency' not specified.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "f1b0729a-c82d-4fc1-8531-fa9950fe2a0b", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder information is missing, violates compliance rule.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/ParentCompany", "Report/LegalStatusSection/Subsidiaries"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "a938d9ae-01e9-4ba6-82d5-15f5ac0d3d24", "question_number": 38, "question": "Calculate credit opinion", "summary": "Credit opinion calculation cannot be verified due to missing payment data.", "confidence_score": 0.85, "relevant_sections": ["Report/PaymentsSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "b7bb6df4-5075-4f1c-b8a5-57f491beb1e1", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Company name 'GenAI25' matches requested name 'GenAI25', compliant.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "1964079b-96af-439e-ab26-15477e6f004c", "question_number": 40, "question": "Town missing in town box", "summary": "Town box is empty, report cannot be sent for edit.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressesSection/Addresses/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "e26c34f2-ac32-4916-8d34-0ea80fe0e18b", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status 'Active' complies; credit can be granted.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-11 16:58:47.301782", "processing_time": 16.***************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "ONLINEMISC", "rag_enabled": true, "permanent_question_bank_used": true}