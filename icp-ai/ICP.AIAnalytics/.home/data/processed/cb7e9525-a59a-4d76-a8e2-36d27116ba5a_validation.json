{"validation_id": "cb7e9525-a59a-4d76-a8e2-36d27116ba5a", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "178980", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "a3498735-c8bb-4c7f-b047-22f621ff0573", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client filtering is enabled but no client code is available for comparison. Question is for client 'TURKEXIM'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "cb28c177-049a-4964-a683-dc587aa235fa", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client filtering is enabled but no client code is available for comparison. Question is for client '??'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "d6e87732-9a88-4c0f-8f29-ca790f612ee7", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name 'MOGZAURI PLUS T/A MPLUS' present, but no Client Specific Comments or Payment details found", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "57e42bbe-7f6e-4322-ba98-6de8122c1c38", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name not present, comments and payment sections should be empty.", "confidence_score": 0.95, "relevant_sections": ["Order Details", "Payments", "Special Notes"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "a515aa90-de04-4c0d-98f5-4a2448c17029", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Cannot verify Gross Profit vs Total Income due to missing data", "confidence_score": 0.85, "relevant_sections": ["Financial", "Profit and Loss", "Total Income", "Total Gross Profit"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "010d6dae-58f8-4665-93b0-dfc72d3c8fbe", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Cannot verify expenses classification due to missing financial data.", "confidence_score": 0.7, "relevant_sections": ["Report/Financial"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "0867f70a-681a-4383-bad6-30720321c11b", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Company name 'MOGZAURI PLUS T/A MPLUS' matches requested name 'MOGZAURI PLUS T/A MPLUS'", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "69c5b6aa-6283-45c6-b3a5-c4a4e091a38d", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client name 'MOGZAURI PLUS T/A MPLUS' present but Client Specific Comments empty", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d635122a-efa7-4512-96c9-ac7c4ccccd77", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not in special note; violates rule for EUR, USD, GBP requests", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "bc16af4c-8f7e-4787-b5d7-27f9895557ca", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "Registration/License numbers missing in LegalStatus/SpecialNotes", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "62891f9f-6ff0-45e4-b099-04a99d8e44fc", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in Payments activities special note", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "57bc5c8f-0df6-448b-8476-c9ef57739311", "question_number": 12, "question": "Spelling Check", "summary": "No spelling errors detected in report content", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "2c894b51-10fb-472f-9a02-ab4803398b78", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "Cannot verify adverse announcements in significant changes", "confidence_score": 0.85, "relevant_sections": ["Significant Changes", "Payments"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "fa380438-8427-488b-a50e-5466058ce0d0", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "No adverse announcements or credit details found, cannot verify compliance.", "confidence_score": 0.85, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5796a848-7816-4f7e-bae3-dd88d71360b2", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "Adverse announcements not found in Change Description.", "confidence_score": 0.9, "relevant_sections": ["Significant Changes"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "7ddc93a0-2c37-4e06-8e75-85c080f37cb7", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section missing sector info; notify editor", "confidence_score": 0.95, "relevant_sections": ["Report/Payments"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "26ec1fed-422d-46b1-a83e-10145594df59", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "No services activities text found; cannot verify export text rule", "confidence_score": 0.9, "relevant_sections": ["Report"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "f7cec6b1-0185-4a42-baed-dc5615cc2824", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income are missing, cannot verify compliance", "confidence_score": 0.9, "relevant_sections": ["Report/ProfitAndLoss", "Report/Income", "Report/TotalIncome"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "8daa06f0-35bf-4976-a280-bccaee2e97ed", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "No credit given, TRA high not found in XML, cannot verify compliance", "confidence_score": 0.85, "relevant_sections": ["Report/SpecialNotesSection", "Report/LegalStatusSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "fc3b00a4-f259-424f-ab76-2ceadf564d86", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not found in Payment section, compliant", "confidence_score": 0.95, "relevant_sections": ["Report"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "dda66230-6640-4838-be48-68539f2d0c6f", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "No classification for dormant/ceased trading found in Payment section", "confidence_score": 0.9, "relevant_sections": ["Report"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "24786a0b-a978-4c5e-9911-4c088046a132", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number not listed as Branch, Transfer to Principal false.", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "de7838ff-6e11-4640-9188-c3e21f915bf3", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Registration number expired, no Comment found", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "1902d8c8-7e9b-4732-899d-611e8f4dd58f", "question_number": 24, "question": "Small with large amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1674. Please try again in 3.348s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "5fc57079-72b7-4aef-b5a1-9a0b52e3ab8b", "question_number": 25, "question": "Large with small amount", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1649. Please try again in 3.298s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "1296d29d-f1c9-4ef0-8673-8f31f3417b83", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications not found, cannot verify compliance", "confidence_score": 0.8, "relevant_sections": ["Personnel/Position", "Position Classifications"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "3d8e2d94-5018-4972-851b-7b9ad0d2bd6f", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "LegalStatusSection present, violates 'Negative' report rule", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "0df03e05-7a48-480c-9824-4869d895e246", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1748. Please try again in 3.496s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "b793357b-f74f-404d-9806-f70f7d5077cd", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Research type 'Decline' not found, cannot verify payment comment requirement", "confidence_score": 0.85, "relevant_sections": ["Report/HeaderSection", "Report/Payments"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "3011355d-1213-49ee-9246-937605f25f6d", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Research Type missing, no 'company contacted' comment or Person Interviewed", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/SpecialNotesSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "5ed8b59d-a337-4ee0-b21f-a95a74c93532", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29177, Requested 1724. Please try again in 1.802s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "5b2c5634-2091-4728-aa57-2dca74b4e8fd", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28972, Requested 1764. Please try again in 1.472s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "dc0dd058-bf3d-432c-a9ca-224d56a6d35f", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client filtering is enabled but no client code is available for comparison. Question is for client 'TURKEXIM'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "a655719e-a70a-4e70-b151-b3aab045aa9c", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29092, Requested 1735. Please try again in 1.654s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "5a5129e3-aae3-482e-b887-4d2725c79b19", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29009, Requested 1753. Please try again in 1.524s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "23ba1817-8f49-42bf-a4eb-0f0115110b1a", "question_number": 36, "question": "Capital missing currency", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29015, Requested 1717. Please try again in 1.464s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "4478470d-aace-433a-96b0-22d9f38cd08d", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29136, Requested 1661. Please try again in 1.594s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "fc52c21c-d9ef-4f3b-9b18-6a0af8479576", "question_number": 38, "question": "Calculate credit opinion", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29049, Requested 1709. Please try again in 1.516s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "ae16f5a9-e650-4d2a-a3fc-ed1e2ee70570", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28930, Requested 1734. Please try again in 1.328s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "8575eea4-ad7d-4627-830f-7e93b7e18421", "question_number": 40, "question": "Town missing in town box", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29025, Requested 1729. Please try again in 1.508s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "3f00f017-376d-4927-aa7b-49a46318df62", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status 'Government Ministry' known, credit can be granted", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/LegalStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-14 15:03:00.592353", "processing_time": 31.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": null, "rag_enabled": true, "permanent_question_bank_used": true}