{"validation_id": "4d82dbdc-0e13-4a3f-a1bb-76f4c84bd4dd", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181489", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "4d24b7b3-4023-4fb4-b4f5-50d5c5a3f9bb", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "42f32d9d-c7b7-40dd-b4be-d647928ed910", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "668d1209-d836-4cc8-a1c7-8034c2bda130", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name missing, cannot verify comments and payment compliance", "confidence_score": 0.9, "relevant_sections": ["OrderDetails/ClientName", "SpecialNotes/TextForSpecificClients", "Payments/CreditOpinionNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "78386502-a461-4235-b027-9a9d8723ca6f", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name missing, but Payment section note 'test' present", "confidence_score": 0.95, "relevant_sections": ["Order Details/Client Name", "PaymentsSection/AdditionalNoteMaxCredit"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "df63ccb8-0a9f-4b9f-a768-b101f259d977", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Cannot verify Gross Profit vs Total Income; data missing in XML", "confidence_score": 0.9, "relevant_sections": ["FinancialSection/ProfitAndLoss", "FinancialSection/TotalIncome", "FinancialSection/TotalGrossProfit"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "cde5d5a9-cd3d-4eea-85bf-e4d73a3e8121", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Expenses for raw materials in 'Operating Costs', violates Direct Costs rule", "confidence_score": 0.95, "relevant_sections": ["FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "9cf6ff44-0ded-4b12-84dc-b41d221622a0", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Requested name missing, cannot verify compliance with 'AishwaryaTest'", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "51b41b88-b639-4e90-bea3-b40d9a1f8a25", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address matches active address, but Client Specific Comment is empty", "confidence_score": 0.95, "relevant_sections": ["Report/AddressSection/Address", "Report/ClientSection/ClientAddress", "Report/ClientSection/ClientSpecificComment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "641ab186-57d2-46b4-b0b8-f70b09780a8a", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max Credit Currency 'AMD' not EUR/USD/GBP, no special note required", "confidence_score": 0.95, "relevant_sections": ["PaymentsSection/MaxCreditCurrency", "PaymentsSection/AdditionalNoteMaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "034275e0-8252-4b60-af4b-3ed8b916f3c4", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "Registration/License numbers missing in LegalSection/SpecialNote", "confidence_score": 0.95, "relevant_sections": ["Report/LegalSection", "Report/SpecialNote"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "9919ec02-b104-48e1-a16e-5c859384ab5a", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in Payments activities special note.", "confidence_score": 0.95, "relevant_sections": ["Report/FinancialSection/BalanceSheets"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "37f18737-4fe0-4bae-8b21-a0c24021045c", "question_number": 12, "question": "Spelling Check", "summary": "Spelling error in 'SourceDescription': 'testt' should be 'test'", "confidence_score": 0.95, "relevant_sections": ["FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "cc7b2cd8-752e-4faa-9b15-62bf51e01026", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1080. Please try again in 2.16s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "83d41601-ded8-4cc7-ade8-f9e37fd6a784", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1018. Please try again in 2.036s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "f547a39a-fe25-4c61-8c26-4eb400f201a1", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found in Change Description section", "confidence_score": 0.9, "relevant_sections": ["SignificantChanges/ChangeClassifications", "SignificantChanges/ChangeDescription"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "163223b9-078c-4aa7-88f3-ad7bf98a272b", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1103. Please try again in 2.206s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "b6c955b4-9743-403e-840d-6e72d933e339", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "No 'Services activities' text found in 'Activities' section", "confidence_score": 0.95, "relevant_sections": ["FinancialSection/BalanceSheets/BalanceSheet/LineItems"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "efdb3452-1d5a-4a77-aa06-7a83c90ada95", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28977, Requested 1169. Please try again in 292ms. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "b5e5ee76-4974-4ebe-9fdd-390a132d6d80", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1015. Please try again in 2.03s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "b2cc96d3-730e-4574-a475-ec678903f542", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 938. Please try again in 1.876s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "8180f78d-96b1-47e2-8694-c310cf5c4a5f", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "TRA 'Average' found, no classification for dormant/ceased trading", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "90670c23-e752-4f9a-ad91-490870af22d8", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 27730, Requested 2988. Please try again in 1.436s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "cede7e78-5c08-4f66-b75d-1779b848fa93", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29134, Requested 2938. Please try again in 4.144s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "********-b29d-40bb-8923-3332d4ecfc71", "question_number": 24, "question": "Small with large amount", "summary": "CreditOpinion 'Large' with MaxCredit '2321' violates 'Small with large amount' rule", "confidence_score": 0.95, "relevant_sections": ["PaymentsSection/CreditOpinion", "PaymentsSection/MaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "49b26aa6-4a7c-49ee-9333-88d0039dceae", "question_number": 25, "question": "Large with small amount", "summary": "CreditOpinion 'Large' with MaxCredit '2321' is small, violates rule", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/CreditOpinion", "Report/PaymentsSection/MaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "df494298-fe65-41cb-b49c-8f36b6b158c7", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29113, Requested 2923. Please try again in 4.072s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "63bd88f0-71f1-452c-a9be-947244439e19", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Research type 'Negative' not found; Payments and Financial sections populated", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/PaymentsSection", "Report/FinancialSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "0a5dec1c-ae63-440f-bb74-501e361654c9", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29335, Requested 2941. Please try again in 4.552s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "dd46f412-67ef-4f35-b7d0-6ea3d9c7bcbf", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29197, Requested 2990. Please try again in 4.374s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "cb975e25-8107-48ec-924c-54e662785dba", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28011, Requested 2984. Please try again in 1.989s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "d831c6d7-9b54-4471-bf1b-316096a509db", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanction Search Performed tag missing, violates rule", "confidence_score": 0.95, "relevant_sections": ["Report/Sanction"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "2ac56eb1-47c5-4e75-bb02-47b479d73181", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29771, Requested 1014. Please try again in 1.57s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "5e2b2481-4203-4778-be66-382299e24271", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "7b8abce1-303b-4419-b08c-4473142c5ef9", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2927. Please try again in 5.854s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "f91e7738-43dd-4728-9670-9cfabff15556", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29601, Requested 2946. Please try again in 5.094s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "0725a591-917d-42c6-bfe6-5e17aac13b1b", "question_number": 36, "question": "Capital missing currency", "summary": "Capital currency field is missing, expected notification of blank field.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatus/Capital", "Report/LegalStatus/Currency"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "12165d4a-8d0e-4899-80f8-5c84d000a6ed", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder info missing for group member 'AishwaryaTest'", "confidence_score": 0.95, "relevant_sections": ["Report/FinancialSection/BalanceSheetFigures/CompanyName"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "09d30824-39e4-4a55-ae0f-d2b9960db7df", "question_number": 38, "question": "Calculate credit opinion", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29809, Requested 2901. Please try again in 5.42s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "0db66b0e-3b81-459d-8085-b0c2b7bad8b6", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29785, Requested 2926. Please try again in 5.422s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "67cbd87d-f9b6-4d8c-ac85-398a2e3edc80", "question_number": 40, "question": "Town missing in town box", "summary": "Town box is empty in address section, violates rule", "confidence_score": 0.95, "relevant_sections": ["Report/Address", "Report/RelatedEntities", "Report/Facilities", "Report/RegisteredAddress"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "3ddcbac9-d97c-43dd-b326-e2e7a5321eda", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status unknown, credit granted; notify editor", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/CreditOpinion", "Report/LegalSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-15 19:28:41.362345", "processing_time": 121.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "0447", "rag_enabled": true, "permanent_question_bank_used": true}