# Environment Variables Configuration
# Copy this file to .env and update with your actual values

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini

# External API Configuration
EXTERNAL_API_BASE_URL=https://wapaz-uks-uta-icpcertsol-api01.azurewebsites.net

# Application Configuration
APP_NAME=XML Report Validation System
APP_VERSION=1.0.0
DEBUG=true
HOST=0.0.0.0
PORT=8000

# Vector Database Configuration
CHROMADB_MODE=embedded
CHROMADB_HOST=localhost
CHROMADB_PORT=8001
CHROMADB_PATH=./data/vector_db
VECTOR_DB_PATH=../VectorDB/embedded

# LangChain Configuration
MAX_TOKENS=10000
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# LLM Semantic Search Configuration
ENABLE_LLM_SEMANTIC_SEARCH=true
SEMANTIC_SEARCH_CONTENT_LIMIT=12000
SEMANTIC_SEARCH_MAX_CHUNKS=3
SEMANTIC_SEARCH_CHUNK_SIZE=8000
SEMANTIC_SEARCH_MIN_EXTRACTION_SIZE=2000

# Performance Configuration
DEFAULT_BATCH_SIZE=10
USE_BULK_PROCESSING=true
ENABLE_PERFORMANCE_OPTIMIZATIONS=true

# File Configuration
MAX_FILE_SIZE=********
UPLOAD_PATH=./data/uploads
PROCESSED_PATH=./data/processed

# Permanent Question Bank Configuration
USE_PERMANENT_QUESTION_BANK=true
PERMANENT_QUESTION_BANK_PATH=./data/Copy of Prompts Checking AI (1).xlsx
PERMANENT_QUESTION_BANK_SHEET=Sheet1
PERMANENT_QUESTION_BANK_AUTO_RELOAD=false
