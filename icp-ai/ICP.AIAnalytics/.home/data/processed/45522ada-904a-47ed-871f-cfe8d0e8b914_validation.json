{"validation_id": "45522ada-904a-47ed-871f-cfe8d0e8b914", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181493", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "45d05d36-bf6f-492c-8eb0-7b1fff205825", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "ce66b45c-bd51-4d92-9995-30b6d1adc816", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "e4ecd378-151b-4aa8-bd07-0c7a45cf2fee", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name is missing; cannot verify compliance for comments and payment sections.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "c4bcab40-90fb-4a11-8b4e-a41c1f810d5f", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name is missing; Client Specific Comments and Payment Section should not be filled.", "confidence_score": 0.98, "relevant_sections": ["Report/OrderDetails/ClientName", "Report/SpecialNotesSection/TextForSpecificClients", "Report/Payments/CreditOpinionNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "10b08847-6373-49c9-86d9-5213409860b4", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Gross Profit and Total Income data missing for compliance check.", "confidence_score": 0.85, "relevant_sections": [], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6009cd9a-0109-436c-a66b-81a649b2ef46", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Expenses for raw materials not found; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": [], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "9bda3bf4-b222-48ed-a96c-b25e27ce2257", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Requested name matches Company Name; Client Specific Comments not needed.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/Requested", "Report/HeaderSection/CompanyName"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8c8606dc-e77c-4fc1-a416-072ff07281c1", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address does not match active address; Client Specific Comments not populated.", "confidence_score": 0.95, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ActiveAddress", "Report/SpecialNotesSection/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "b98e4dc5-ff4e-4cc3-bc70-4d8521677f59", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max credit not found in special notes; compliance violated.", "confidence_score": 0.95, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "e3bb9be7-6f75-4d90-b72d-8154d7773c66", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "Registration/license numbers missing in legal section and special notes.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/RegistrationNumbers", "Report/SpecialNotesSection/TextForSpecificClients"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "3f161b17-e747-4982-8cce-cff9d562ef5f", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "No biased or colloquial language found in special notes section.", "confidence_score": 0.98, "relevant_sections": ["Report/SpecialNotesSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "acf3daa3-317b-4e7b-b801-6da1d339c270", "question_number": 12, "question": "Spelling Check", "summary": "Spelling check passed; no violations found in relevant sections.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/LegalStatusSection/ICPLegalGroup", "Report/WaiverSection/Waiver"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "3ccf752f-3143-4753-a024-d7e7b5427689", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "Payments section lacks reference to significant changes; violation found.", "confidence_score": 0.98, "relevant_sections": ["Report/SignificantChanges", "Report/Payments/CreditOpinionNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "8b2218d9-b2e8-4d91-97c3-7dd9aa06df23", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Compliance violated: missing significant changes and payment sections.", "confidence_score": 0.9, "relevant_sections": ["Report/SignificantChangesSection", "Report/PaymentSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6960c4b1-b5ff-46bd-9391-cd1ac2c0a43a", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "No adverse announcements found; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": [], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "6c1f375d-82dd-4e3b-b25e-05e17795b597", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks relevant sector information from Subject activities.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "87ec6306-8ebc-459a-bbc7-64f54d92b947", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Violation found: Incorrect text added regarding export activities.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/ICPLegalGroup", "Report/LegalStatusSection/LegalStatusAdditional"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "9909cdb5-4091-431a-b9cd-b291174d93f7", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income are both missing, violating the validation rule.", "confidence_score": 0.98, "relevant_sections": ["Report/ProfitAndLoss/Income", "Report/ProfitAndLoss/TotalIncome"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "0ea57499-e8f7-421d-957f-462fdd456b65", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Violation: Credit opinion currency not requested; TRA not high.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/LegalStatusSection/Capital/NotGiven"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b764fc09-c297-4a6d-a1f9-c01806b82bf3", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not present where applicable; violation found.", "confidence_score": 0.9, "relevant_sections": ["Report/AddressesSection/Addresses/Address/ISO2CountryCode", "Report/AddressesSection/Addresses/Address/ICPCountryCode"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6824c902-f613-4aa0-bee6-d7c2b9035295", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Company status is 'Active'; no violation of dormant or ceased trading rule.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus", "Report/AddressesSection/Addresses/Address/ActiveAddress"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "c5ff54af-410b-4cb5-8e3a-52e4db20d1e0", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number missing; compliance cannot be verified.", "confidence_score": 0.9, "relevant_sections": ["Report/SpecialNotesSection/TransferToPrincipal", "Report/LegalStatusSection/Subsidiaries"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "8edebb13-ad67-454e-8203-291001fb3487", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Registration number expired with no comment provided.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ae3084e4-fa5d-4a1d-b27f-c3b5c311de06", "question_number": 24, "question": "Small with large amount", "summary": "PaidUp capital of 123.00 is small compared to the large amount expected, violating the rule.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "aa461160-7b8a-4638-a1ce-2f9bbf76f2ff", "question_number": 25, "question": "Large with small amount", "summary": "PaidUp capital of 123.00 violates large with small amount rule.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "b96ab12b-e1d2-4b12-b38e-a1dd3f9aa3af", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications are not provided; compliance cannot be verified.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "5fd8ffa4-a672-40aa-b2b9-f34c8adf8525", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Validation rejected: Missing 'Research Type' as 'Negative' and special notes not reflecting location issues.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/SpecialNotesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "90da5e65-fa7e-48e4-a00c-b40154cc7e69", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Validation failed: Research Type is 'Decline', Person Interviewed is missing.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/SpecialNotesSection/PersonInterviewed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "22f7c019-6032-4fe4-857a-35076163815f", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Validation failed: Payments section lacks comment on information decline.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/SpecialNotesSection"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "2393f0ca-371c-4dbf-801c-27a2f3fdff1a", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Validation failed: Research Type is missing and no comments or Person Interviewed found.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/Payments/CreditOpinionNotes", "Report/SpecialNotesSection/PersonInterviewed"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "ddddc89d-adb0-4662-a463-8ab2f7c2544d", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanctions search not performed; compliance rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "5daa0ded-9307-4797-b0c0-da9f79e6cf78", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "No sanctions found; compliance rule not met.", "confidence_score": 0.9, "relevant_sections": ["Report/SanctionSection/SanctionSearchPerformed", "Report/SanctionSection/SanctionFound"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "a897f847-87fd-4480-ba30-c8fb2402a58f", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client 'ONLINEMISC'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "889d90a2-9d19-44ee-b5aa-07ad9e9c20aa", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in Header and LegalStatus sections.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/LegalStatusSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "edd2b897-deb0-4a8e-aab9-cfdc0b9799ac", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "No contact details present; compliance rule violated.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/ResearchType", "Report/AddressesSection/Addresses/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "1da102bf-b972-4e7b-831a-16112de7ae24", "question_number": 36, "question": "Capital missing currency", "summary": "Capital currency field is missing; expected a currency specification.", "confidence_score": 0.9, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp", "Report/LegalStatusSection/Capital"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "8536cffc-7761-4cfb-88d4-d320c09c57db", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder field is not populated, violating compliance rule.", "confidence_score": 0.98, "relevant_sections": ["Report/LegalStatusSection/ParentCompany", "Report/LegalStatusSection/Subsidiaries"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "a13aae4f-e4f4-44e2-a8b3-5c248d2be460", "question_number": 38, "question": "Calculate credit opinion", "summary": "Cannot verify credit opinion calculation due to missing payment data.", "confidence_score": 0.85, "relevant_sections": ["Report/LegalStatusSection/Capital/PaidUp"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "306f495c-a010-4ea5-833c-4af59b134cf3", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Names in financials match header; compliance rule is satisfied.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "67c8272b-c2a4-483d-8e54-04c292d5973b", "question_number": 40, "question": "Town missing in town box", "summary": "Violation: Town box is empty in the address section.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressesSection/Addresses/Address"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "5486c5cc-f719-49fb-bec7-80ebb0d09b63", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status is active; credit can be granted, compliance met.", "confidence_score": 0.95, "relevant_sections": ["Report/LegalStatusSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-11 14:33:13.552600", "processing_time": 17.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "ONLINEMISC", "rag_enabled": true, "permanent_question_bank_used": true}