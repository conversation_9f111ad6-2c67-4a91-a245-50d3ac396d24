from fastapi import APIRouter, File, UploadFile, HTTPException, Depends
from fastapi.responses import J<PERSON><PERSON>esponse, Response
from typing import List
import os
import base64
import httpx
from datetime import datetime

from app.core.config import settings
from app.models.schemas import FileUploadR<PERSON>ponse, QuestionsUploadResponse, ExternalAPIRequest, OrderDetailsRequest
from app.services.file_processor import FileProcessor
from app.utils.xml_generator import XMLGenerator


router = APIRouter()
file_processor = FileProcessor()
xml_generator = XMLGenerator()


def validate_file_upload(file: UploadFile, allowed_extensions: List[str], max_size: int = None):
    """Validate uploaded file."""
    if not file.filename:
        raise HTTPException(status_code=400, detail="No filename provided")
    
    # Check file extension
    file_extension = os.path.splitext(file.filename)[1].lower()
    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid file type. Allowed types: {', '.join(allowed_extensions)}"
        )
    
    # Check file size if specified
    if max_size and hasattr(file, 'size') and file.size > max_size:
        raise HTTPException(
            status_code=400, 
            detail=f"File too large. Maximum size: {max_size} bytes"
        )


@router.post("/upload/questions", response_model=QuestionsUploadResponse)
async def upload_questions(
    file: UploadFile = File(...),
    response_format: str = "json"
):
    """Upload Excel file containing validation questions."""
    try:
        # Validate file
        validate_file_upload(file, settings.ALLOWED_EXCEL_EXTENSIONS, settings.MAX_FILE_SIZE)
        
        # Read file content
        file_content = await file.read()
        
        # Save uploaded file
        file_id = await file_processor.save_uploaded_file(file_content, file.filename)
        
        # Process Excel file to extract questions
        questions = await file_processor.process_excel_questions(file_id)
        
        # Create response data
        response_data = {
            "file_id": file_id,
            "filename": file.filename,
            "total_questions": len(questions),
            "questions": [q.model_dump() for q in questions],
            "upload_timestamp": datetime.now()
        }
        
        # Return appropriate format
        if response_format.lower() == "xml":
            xml_response = xml_generator.generate_file_upload_xml(response_data)
            return Response(content=xml_response, media_type="application/xml")
        else:
            return QuestionsUploadResponse(**response_data)
            
    except HTTPException:
        # Re-raise HTTPException to let FastAPI handle it properly
        raise
    except Exception as e:
        import traceback
        error_details = f"Error: {str(e)}\nTraceback: {traceback.format_exc()}"
        print(f"Upload error: {error_details}")
        
        if response_format.lower() == "xml":
            error_xml = xml_generator.generate_error_xml(str(e), "UPLOAD_ERROR")
            return Response(content=error_xml, media_type="application/xml", status_code=500)
        else:
            raise HTTPException(status_code=500, detail=str(e))


@router.post("/upload/report", response_model=FileUploadResponse)
async def upload_report(
    file: UploadFile = File(...),
    response_format: str = "json"
):
    """Upload XML report file for validation."""
    try:
        # Validate file
        validate_file_upload(file, settings.ALLOWED_XML_EXTENSIONS, settings.MAX_FILE_SIZE)
        
        # Read file content
        file_content = await file.read()
        
        # Save uploaded file
        file_id = await file_processor.save_uploaded_file(file_content, file.filename)
        
        # Process XML file
        await file_processor.process_xml_report(file_id)
        
        # Create response data
        response_data = {
            "file_id": file_id,
            "filename": file.filename,
            "file_type": os.path.splitext(file.filename)[1].lower(),
            "file_size": len(file_content),
            "upload_timestamp": datetime.now(),
            "status": "uploaded"
        }
        
        # Return appropriate format
        if response_format.lower() == "xml":
            xml_response = xml_generator.generate_file_upload_xml(response_data)
            return Response(content=xml_response, media_type="application/xml")
        else:
            return FileUploadResponse(**response_data)
            
    except Exception as e:
        if response_format.lower() == "xml":
            error_xml = xml_generator.generate_error_xml(str(e), "UPLOAD_ERROR")
            return Response(content=error_xml, media_type="application/xml", status_code=500)
        else:
            raise HTTPException(status_code=500, detail=str(e))


@router.post("/upload/report-from-api", response_model=FileUploadResponse)
async def fetch_report_from_api(
    request: ExternalAPIRequest,
    response_format: str = "json"
):
    """Fetch XML report from external API and process it."""
    try:
        # API configuration - Updated to new endpoint
        api_url = f"{settings.EXTERNAL_API_BASE_URL}/api/report/getOrderXml?reportId={request.report_id}"
        headers = {
            "Authorization": f"Bearer {request.bearer_token}",
            "Content-Type": "application/json"
        }
        
        # Fetch data from external API
        async with httpx.AsyncClient(timeout=30.0) as client:
            api_response = await client.get(api_url, headers=headers)
            
            if api_response.status_code != 200:
                raise HTTPException(
                    status_code=api_response.status_code, 
                    detail=f"External API error: {api_response.text}"
                )
            
            # Parse the API response
            try:
                api_data = api_response.json()
            except Exception as e:
                # If JSON parsing fails, the response might be plain text (possibly base64)
                response_text = api_response.text.strip()
                if len(response_text) > 100:  # Likely base64 content
                    base64_xml = response_text
                else:
                    raise HTTPException(
                        status_code=422, 
                        detail=f"Invalid API response format. Expected JSON or base64 string. Error: {str(e)}"
                    )
            else:
                # Extract base64 XML content from JSON response
                base64_xml = None
                possible_keys = ['data', 'xml', 'content', 'report', 'xmlData', 'xmlContent', 'value', 'result']
                
                # First, try common keys
                if isinstance(api_data, dict):
                    for key in possible_keys:
                        if key in api_data:
                            base64_xml = api_data[key]
                            break
                    
                    # If none of the common keys work, try to find any string value that looks like base64
                    if not base64_xml:
                        for key, value in api_data.items():
                            if isinstance(value, str) and len(value) > 100:  # Likely base64 string
                                base64_xml = value
                                break
                elif isinstance(api_data, str):
                    # Response is a direct string (might be base64)
                    base64_xml = api_data
                
                if not base64_xml:
                    raise HTTPException(
                        status_code=422, 
                        detail=f"Could not find base64 XML content in API response. Response structure: {type(api_data).__name__}, Available keys: {list(api_data.keys()) if isinstance(api_data, dict) else 'N/A'}"
                    )
        
        # Decode base64 to XML
        try:
            xml_content = base64.b64decode(base64_xml).decode('utf-8')
        except Exception as e:
            raise HTTPException(
                status_code=422, 
                detail=f"Failed to decode base64 content: {str(e)}"
            )
        
        # Validate that it's XML
        try:
            import xml.etree.ElementTree as ET
            ET.fromstring(xml_content)
        except ET.ParseError as e:
            raise HTTPException(
                status_code=422, 
                detail=f"Invalid XML content: {str(e)}"
            )
        
        # Create filename based on report ID
        filename = f"report_{request.report_id}_xml.xml"
        
        # Save the XML content as if it was uploaded
        file_content = xml_content.encode('utf-8')
        additional_metadata = {
            "source": "external_api",
            "report_id": request.report_id
        }
        file_id = await file_processor.save_uploaded_file(file_content, filename, additional_metadata)
        
        # Process the XML report
        await file_processor.process_xml_report(file_id)
        
        # Create response data
        response_data = {
            "file_id": file_id,
            "filename": filename,
            "file_type": ".xml",
            "file_size": len(file_content),
            "upload_timestamp": datetime.now(),
            "status": "uploaded",
            "source": "external_api",
            "report_id": request.report_id  # Updated field name
        }
        
        # Return appropriate format
        if response_format.lower() == "xml":
            xml_response = xml_generator.generate_file_upload_xml(response_data)
            return Response(content=xml_response, media_type="application/xml")
        else:
            return FileUploadResponse(**response_data)
            
    except HTTPException:
        raise
    except Exception as e:
        if response_format.lower() == "xml":
            error_xml = xml_generator.generate_error_xml(str(e), "API_FETCH_ERROR")
            return Response(content=error_xml, media_type="application/xml", status_code=500)
        else:
            raise HTTPException(status_code=500, detail=f"Error fetching report from API: {str(e)}")


@router.post("/upload/order-details")
async def fetch_order_details(
    request: OrderDetailsRequest,
    response_format: str = "json"
):
    """
    Fetch order details from external API to get client code.
    
    Expected API response structure:
    {
        "clientId": 383,
        "clientShortCode": "0447",  # This is the primary client code field
        "clientReference": "tudip1Report",
        "country": "India",
        "companyName": "tudip1",
        ...
    }
    """
    try:
        # API configuration
        api_url = f"{settings.EXTERNAL_API_BASE_URL}/api/order/getOrderDetails?csr={request.csr_id}&copy={request.copy}&version={request.version}"
        headers = {
            "Authorization": f"Bearer {request.bearer_token}",
            "Content-Type": "application/json"
        }
        
        # Fetch data from external API
        async with httpx.AsyncClient(timeout=30.0) as client:
            api_response = await client.get(api_url, headers=headers)
            
            if api_response.status_code != 200:
                raise HTTPException(
                    status_code=api_response.status_code, 
                    detail=f"External API error: {api_response.text}"
                )
            
            # Parse the API response
            try:
                api_data = api_response.json()
            except Exception as e:
                raise HTTPException(
                    status_code=422, 
                    detail=f"Invalid API response format. Expected JSON. Error: {str(e)}"
                )
            
            # Extract client code from response
            client_code = None
            # Updated to prioritize clientShortCode based on API response structure
            possible_client_keys = [
                'clientShortCode',  # Primary field based on API response
                'clientCode', 'client_code', 'ClientCode', 'Client_Code',
                'client', 'Client', 'clientId', 'ClientId', 'client_id'
            ]
            
            # Search for client code in the response
            if isinstance(api_data, dict):
                for key in possible_client_keys:
                    if key in api_data:
                        client_code = str(api_data[key])
                        break
                
                # If not found in root, search nested objects
                if not client_code:
                    for key, value in api_data.items():
                        if isinstance(value, dict):
                            for nested_key in possible_client_keys:
                                if nested_key in value:
                                    client_code = str(value[nested_key])
                                    break
                            if client_code:
                                break
            
            # Create response data
            response_data = {
                "csr_id": request.csr_id,
                "copy": request.copy,
                "version": request.version,
                "client_code": client_code,
                "api_response": api_data,
                "fetch_timestamp": datetime.now()
            }
            
            if response_format.lower() == "xml":
                xml_response = xml_generator.generate_file_upload_xml(response_data)
                return Response(content=xml_response, media_type="application/xml")
            else:
                return response_data
                
    except HTTPException:
        raise
    except Exception as e:
        if response_format.lower() == "xml":
            error_xml = xml_generator.generate_error_xml(str(e), "ORDER_DETAILS_ERROR")
            return Response(content=error_xml, media_type="application/xml", status_code=500)
        else:
            raise HTTPException(status_code=500, detail=f"Error fetching order details: {str(e)}")


@router.get("/upload/files")
async def list_uploaded_files():
    """List all uploaded files."""
    try:
        files = []
        upload_path = file_processor.upload_path
        processed_path = file_processor.processed_path
        
        # Get all metadata files
        for metadata_file in processed_path.glob("*_metadata.json"):
            metadata = await file_processor.get_file_metadata(
                metadata_file.stem.replace("_metadata", "")
            )
            if metadata:
                files.append(metadata)
        
        return {"files": files, "total_count": len(files)}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/upload/questions/{file_id}")
async def get_questions(file_id: str, response_format: str = "json"):
    """Get processed questions by file ID."""
    try:
        questions = await file_processor.get_processed_questions(file_id)
        if not questions:
            raise HTTPException(status_code=404, detail="Questions not found")
        
        metadata = await file_processor.get_file_metadata(file_id)
        
        response_data = {
            "file_id": file_id,
            "filename": metadata.get("original_filename", "") if metadata else "",
            "total_questions": len(questions),
            "questions": [q.model_dump() for q in questions],
            "upload_timestamp": metadata.get("upload_timestamp", "") if metadata else ""
        }
        
        if response_format.lower() == "xml":
            xml_response = xml_generator.generate_file_upload_xml(response_data)
            return Response(content=xml_response, media_type="application/xml")
        else:
            return response_data
            
    except HTTPException:
        raise
    except Exception as e:
        if response_format.lower() == "xml":
            error_xml = xml_generator.generate_error_xml(str(e), "RETRIEVAL_ERROR")
            return Response(content=error_xml, media_type="application/xml", status_code=500)
        else:
            raise HTTPException(status_code=500, detail=str(e))


@router.get("/upload/report/{file_id}")
async def get_report_info(file_id: str):
    """Get report information by file ID."""
    try:
        report_data = await file_processor.get_processed_report(file_id)
        if not report_data:
            raise HTTPException(status_code=404, detail="Report not found")
        
        metadata = await file_processor.get_file_metadata(file_id)
        
        # Return summary information (not full content for security)
        return {
            "file_id": file_id,
            "filename": metadata.get("original_filename", "") if metadata else "",
            "root_element": report_data.get("root_element", ""),
            "text_sections_count": len(report_data.get("text_content", [])),
            "processing_timestamp": report_data.get("processing_timestamp", ""),
            "upload_timestamp": metadata.get("upload_timestamp", "") if metadata else ""
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/upload/{file_id}")
async def delete_file(file_id: str):
    """Delete uploaded file and its processed data."""
    try:
        # Get metadata
        metadata = await file_processor.get_file_metadata(file_id)
        if not metadata:
            raise HTTPException(status_code=404, detail="File not found")
        
        # Delete files
        upload_path = file_processor.upload_path
        processed_path = file_processor.processed_path
        
        files_to_delete = [
            upload_path / metadata["saved_filename"],
            processed_path / f"{file_id}_metadata.json",
            processed_path / f"{file_id}_questions.json",
            processed_path / f"{file_id}_report.json"
        ]
        
        deleted_files = []
        for file_path in files_to_delete:
            if file_path.exists():
                file_path.unlink()
                deleted_files.append(str(file_path))
        
        return {
            "message": "File deleted successfully",
            "file_id": file_id,
            "deleted_files": deleted_files
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 