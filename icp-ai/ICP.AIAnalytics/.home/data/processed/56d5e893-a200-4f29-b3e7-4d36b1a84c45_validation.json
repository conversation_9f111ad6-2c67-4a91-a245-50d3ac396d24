{"validation_id": "56d5e893-a200-4f29-b3e7-4d36b1a84c45", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181489", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "5697028e-7c44-4eac-ae08-b40eae765b23", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "53cabbe7-24a1-4c5a-aeab-9990a03d2b95", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "f5cc2ff1-7c85-492c-a208-a329c48665e4", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name missing, cannot verify comments and payment compliance", "confidence_score": 0.9, "relevant_sections": ["OrderDetails/ClientName", "SpecialNotes/TextForSpecificClients", "Payments/CreditOpinionNotes"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "7cab7daf-30e3-475d-bcab-0ce2153d256f", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name missing, but Payment Section has notes, violating rule", "confidence_score": 0.95, "relevant_sections": ["Order Details/Client Name", "PaymentsSection/AdditionalNoteMaxCredit"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "963944ab-f55f-49ef-91bf-d9a4dd91da85", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Gross Profit 80000.00 is less than Total Income 100000.00, compliant", "confidence_score": 0.95, "relevant_sections": ["FinancialSection/BalanceSheets/BalanceSheet/LineItems"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "30a5fa8f-ebb2-4f8b-8c25-cef20da4bf43", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "Expenses for raw materials in 'Operating Costs', violates Direct Costs rule", "confidence_score": 0.95, "relevant_sections": ["FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "a5b3fe52-074f-4369-ab95-d81b3e56c334", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Requested name missing; cannot verify compliance with 'AishwaryaTest'.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/HeaderSection/Requested"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "73c4da7b-21cc-43d0-8b71-9bbdbada33ce", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address '456 Secondary St' inactive, comment populated", "confidence_score": 0.98, "relevant_sections": ["Report/AddressSection/Address", "Report/ClientSection/ClientSpecificComment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "ecb6ced4-2f7d-47f1-b8a1-618f47892e9d", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max Credit Currency 'AMD' not EUR/USD/GBP, no special note needed", "confidence_score": 0.95, "relevant_sections": ["PaymentsSection/MaxCreditCurrency", "PaymentsSection/AdditionalNoteMaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "497a6975-3564-43f4-90ae-984d237bba04", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "Registration/license numbers missing in LegalSection/SpecialNote", "confidence_score": 0.95, "relevant_sections": ["Report/LegalSection", "Report/SpecialNote"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "720cbee1-cd72-47b9-98ca-5041fc134951", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1159. Please try again in 2.318s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "660efca3-08ab-48bb-aa17-d831bce4ac53", "question_number": 12, "question": "Spelling Check", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1380. Please try again in 2.76s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "4bbe0494-18f1-4af7-a733-70450bff878b", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1080. Please try again in 2.16s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "390ed746-0050-4eee-a84a-0f1ece74040e", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1043. Please try again in 2.086s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "876ebe33-4279-4419-8e49-1f44032d0d39", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 918. Please try again in 1.836s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "be80516f-e7ce-4b6d-941f-926b5ad761ee", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1103. Please try again in 2.206s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "d559fb4a-88b6-4033-a246-f7e4f0d16261", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2878. Please try again in 5.756s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "ece38b21-5d44-4b57-a65b-363c974f73f8", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1169. Please try again in 2.338s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "b63d520f-8853-4886-9efe-e851cecb5e0b", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2915. Please try again in 5.83s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "e0f5bcac-dd5b-4165-b739-e0fed3bf2a9a", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 938. Please try again in 1.876s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "6f174f69-c7fa-4890-b616-9290e5d0687e", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29090, Requested 939. Please try again in 58ms. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "213cc754-9114-4f1c-95b6-be0af37135c2", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2988. Please try again in 5.976s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "e92f700f-a12b-47e4-8252-4f69a02fc81e", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 29736, Requested 2938. Please try again in 5.348s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "b11cabf8-16d2-4824-858c-04bda7b7285e", "question_number": 24, "question": "Small with large amount", "summary": "Max Credit '2321' with 'Large' opinion violates 'Small with large amount' rule", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/CreditOpinion", "Report/PaymentsSection/MaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "165ac420-821b-429b-b039-9fc78c6fd8f3", "question_number": 25, "question": "Large with small amount", "summary": "CreditOpinion 'Large' with MaxCredit '2321' is a violation of 'Large with small amount' rule", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/CreditOpinion", "Report/PaymentsSection/MaxCredit"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "c135ef7e-df73-4ec6-a6ca-df81951d7ba2", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications not found, cannot verify compliance", "confidence_score": 0.85, "relevant_sections": ["Personnel/Position", "PositionClassifications"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "5c988d68-c383-4835-aa6b-b9cfe80196d5", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2973. Please try again in 5.946s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "72df5c42-d671-46ef-a4c9-20e4763ec462", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2941. Please try again in 5.882s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "767eb09d-8740-447d-91c4-36901b0abbe6", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2990. Please try again in 5.98s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "add06e3f-e4cd-46e4-a1ed-7439e93f2b8c", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 2984. Please try again in 5.968s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "440b9ff0-7d8e-4a12-8002-49c03304d1fb", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 947. Please try again in 1.894s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "239ce59b-5830-4024-9f59-024118acc092", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1014. Please try again in 2.027s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "a42aa203-ee26-449c-8ad6-21872660c5a3", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "da06bd50-85e3-45d7-9d84-6ce872c29d82", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in FinancialSection/BalanceSheets/LineItems", "confidence_score": 0.95, "relevant_sections": ["FinancialSection/BalanceSheets/LineItems"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "277fcda0-a6e3-44b3-aa76-eb5f44937e7f", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "Research type 'Select' but no contact details found", "confidence_score": 0.95, "relevant_sections": ["Report/HeaderSection", "Report/AddressSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "d2361deb-49cd-472b-a7da-8e2c7c2312f9", "question_number": 36, "question": "Capital missing currency", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 992. Please try again in 1.984s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "6c77cd17-8089-48ed-ade0-fc0d2e7d09b3", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder not populated for 'AishwaryaTest', violates rule", "confidence_score": 0.95, "relevant_sections": ["Report/FinancialSection/BalanceSheetFigures/CompanyName"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4b1924ed-847b-41dc-9ccf-7a2fcef9372c", "question_number": 38, "question": "Calculate credit opinion", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 1000. Please try again in 2s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "f7f7dfef-558e-49b6-977c-db90722a5bcb", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 28735, Requested 2926. Please try again in 3.322s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "282a872d-805a-41f1-835d-4d14060ff1a2", "question_number": 40, "question": "Town missing in town box", "summary": "Error validating question: Error generating enhanced LLM response: Error code: 429 - {'error': {'message': 'Rate limit reached for gpt-4o in organization org-2e8kYoov6j9Gptm17TdG2yCh on tokens per min (TPM): Limit 30000, Used 30000, Requested 985. Please try again in 1.97s. Visit https://platform.openai.com/account/rate-limits to learn more.', 'type': 'tokens', 'param': None, 'code': 'rate_limit_exceeded'}}", "confidence_score": 0.0, "relevant_sections": [], "status": "error", "client_match_status": "no_client_code"}, {"question_id": "981f17f4-755c-4881-819e-d8773c976716", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status unknown, credit granted; violates rule", "confidence_score": 0.95, "relevant_sections": ["Report/PaymentsSection/CreditOpinion", "Report/LegalSection/CompanyStatus"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-15 19:02:26.485065", "processing_time": 116.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "0447", "rag_enabled": true, "permanent_question_bank_used": true}