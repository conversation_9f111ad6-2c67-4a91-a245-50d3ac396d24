<?xml version="1.0" encoding="UTF-8"?>
<DataProcessingReport xmlns="http://example.com/data-processing" version="2.1">
    <ReportMetadata>
        <ReportId>RPT-2024-001</ReportId>
        <GeneratedBy>Data Processing System v3.2</GeneratedBy>
        <GeneratedOn>2024-01-15T10:30:00Z</GeneratedOn>
        <LastUpdated>2024-01-15T14:45:00Z</LastUpdated>
        <DataSource>Customer Transaction Database</DataSource>
    </ReportMetadata>
    
    <ProcessingSummary>
        <TotalRecords>250000</TotalRecords>
        <ProcessedRecords>248750</ProcessedRecords>
        <CompletionPercentage>99.5</CompletionPercentage>
        <ProcessingMethod>Batch Processing with ETL Pipeline</ProcessingMethod>
        <ProcessingDuration>PT2H15M</ProcessingDuration>
        <FileSize unit="MB">1024.5</FileSize>
    </ProcessingSummary>
    
    <DataQuality>
        <QualityScore>95.8</QualityScore>
        <IssuesIdentified>true</IssuesIdentified>
        <ValidationErrors>125</ValidationErrors>
        <DuplicateRecords>false</DuplicateRecords>
        <QualityMetrics>
            <Completeness>98.2</Completeness>
            <Accuracy>97.5</Accuracy>
            <Consistency>94.1</Consistency>
            <Validity>96.8</Validity>
        </QualityMetrics>
        <IssueDetails>
            <Issue>
                <Type>Missing Values</Type>
                <Count>1250</Count>
                <Severity>Medium</Severity>
                <Description>Email addresses missing for 1250 customer records</Description>
            </Issue>
            <Issue>
                <Type>Format Inconsistency</Type>
                <Count>75</Count>
                <Severity>Low</Severity>
                <Description>Phone number format variations across records</Description>
            </Issue>
        </IssueDetails>
    </DataQuality>
    
    <ValidationResults>
        <RequiredFields>
            <AllPopulated>false</AllPopulated>
            <PopulationRate>99.2</PopulationRate>
            <MissingFields>
                <Field name="email">1250</Field>
                <Field name="phone">500</Field>
            </MissingFields>
        </RequiredFields>
        <BusinessRules>
            <RulePassed>true</RulePassed>
            <RulesEvaluated>45</RulesEvaluated>
            <RulesPassed>43</RulesPassed>
            <RulesFailed>2</RulesFailed>
        </BusinessRules>
    </ValidationResults>
    
    <PerformanceMetrics>
        <ProcessingSpeed>1152.78</ProcessingSpeed>
        <ThroughputRate unit="records_per_second">30.7</ThroughputRate>
        <MemoryUsage unit="MB">2048</MemoryUsage>
        <CPUUtilization>75.3</CPUUtilization>
    </PerformanceMetrics>
    
    <Errors>
        <ErrorCount>25</ErrorCount>
        <CriticalErrors>0</CriticalErrors>
        <Warnings>125</Warnings>
        <ErrorLog>
            <Error>
                <Timestamp>2024-01-15T12:15:30Z</Timestamp>
                <Level>Warning</Level>
                <Code>W001</Code>
                <Message>Data type mismatch in field 'age' - converted to integer</Message>
                <RecordId>REC-450123</RecordId>
            </Error>
            <Error>
                <Timestamp>2024-01-15T12:18:45Z</Timestamp>
                <Level>Warning</Level>
                <Code>W002</Code>
                <Message>Phone number format standardized</Message>
                <RecordId>REC-450124</RecordId>
            </Error>
        </ErrorLog>
    </Errors>
    
    <OutputFiles>
        <ProcessedData>
            <FileName>processed_customer_data_2024_01_15.csv</FileName>
            <FileSize unit="MB">856.2</FileSize>
            <RecordCount>248750</RecordCount>
            <Location>/output/processed/</Location>
        </ProcessedData>
        <ErrorReport>
            <FileName>error_report_2024_01_15.log</FileName>
            <FileSize unit="KB">15.7</FileSize>
            <Location>/output/logs/</Location>
        </ErrorReport>
    </OutputFiles>
    
    <Configuration>
        <ProcessingRules>
            <DataCleaning>enabled</DataCleaning>
            <Deduplication>enabled</Deduplication>
            <Validation>strict</Validation>
            <ErrorHandling>continue_on_warning</ErrorHandling>
        </ProcessingRules>
        <Parameters>
            <BatchSize>1000</BatchSize>
            <MaxRetries>3</MaxRetries>
            <TimeoutMinutes>120</TimeoutMinutes>
        </Parameters>
    </Configuration>
    
    <Recommendations>
        <Recommendation priority="high">
            <Title>Improve Email Data Collection</Title>
            <Description>Implement validation at data entry to ensure email addresses are captured</Description>
        </Recommendation>
        <Recommendation priority="medium">
            <Title>Standardize Phone Number Format</Title>
            <Description>Implement consistent phone number formatting across all data sources</Description>
        </Recommendation>
    </Recommendations>
    
    <Footer>
        <ReportStatus>Completed</ReportStatus>
        <NextScheduledRun>2024-01-16T10:00:00Z</NextScheduledRun>
        <ContactInfo><EMAIL></ContactInfo>
    </Footer>
</DataProcessingReport> 