{"validation_id": "f581a2e9-3503-4ef9-b76c-e04a321d077f", "status": "completed", "questions_file_id": "permanent", "questions_source": "permanent_question_bank", "report_id": "181489", "total_questions": 41, "processed_questions": 41, "skipped_questions": 3, "results": [{"question_id": "3112ff91-fa4c-4816-826d-81a6268bf898", "question_number": 1, "question": "Max Credit Currency must be EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "7f15c098-0980-420a-a2a7-e8fe644e150f", "question_number": 2, "question": "Max Credit Currency should be the same currency as the company location EUR or USD", "summary": "Question skipped - Client code mismatch. Question is for client '??' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "d7bd5157-640f-42f6-aa99-3da9346717b1", "question_number": 3, "question": "If the client name is present in the 'Client Name' field on the Order Details page, should it be mandatory to include the corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client name not found; cannot verify comments or payment section compliance.", "confidence_score": 0.85, "relevant_sections": ["Report/PaymentsSection/CreditOpinion", "Report/PaymentsSection/PaymentHistory"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "fce75078-4be5-467d-8589-5c8937670e0c", "question_number": 4, "question": "If the client name is NOT present in the 'Client Name' field on the Order Details page, NO notes should be present in corresponding details in both the 'Client Specific Comments to be Included in the Report' section and the 'Payment' section?", "summary": "Client Name not present; Payment section has notes, violating compliance.", "confidence_score": 0.98, "relevant_sections": ["Report/PaymentsSection/AdditionalNoteMaxCredit", "Report/ClientSpecificComments"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "67f1a234-2d76-47d1-91a7-f1dc10a78caf", "question_number": 5, "question": "In the financial section Gross Profit should be less than Total Income", "summary": "Gross Profit '80000.00' is less than Total Income '100000.00', compliant.", "confidence_score": 0.98, "relevant_sections": ["FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem[1]", "FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem[2]"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "b2c41312-3da6-4fb2-8ea6-87439f20bf57", "question_number": 6, "question": "Financial info section Expenses for raw materials and supplies should be in Direct Costs instead of Operating Costs", "summary": "No expenses for raw materials found; cannot verify compliance.", "confidence_score": 0.85, "relevant_sections": ["FinancialSection/ProfitAndLoss"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "e8f94c08-621f-404a-bb9b-494762ba4a05", "question_number": 7, "question": "Company name provided in the order as \"Requested\" does this match the Official company name. If this does not match Requested Name and Client Specific comment should be populated", "summary": "Company name 'AishwaryaTest' matches requested name, comments not needed.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "15e2142e-1735-48c3-b94a-96cdf5e2fd33", "question_number": 8, "question": "Does address that the client provides match any address in the Address section. If the address provided by the client is not the active address, Client Specific comment should be populated", "summary": "Client address '123 Main St' matches active address, comments not needed.", "confidence_score": 0.98, "relevant_sections": ["Report/AddressSection/Address", "Report/ClientSpecificComment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "037b9766-c4ae-4104-b6af-0d6689a90324", "question_number": 9, "question": "If the client  requests maximum credit in EUR, USD, GBP and not TURKEXIM, max credit should be in special note", "summary": "Max Credit Currency is 'AMD', violates EUR/USD/GBP requirement.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/MaxCreditCurrency", "PaymentsSection/MaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "73abdf42-7a53-43cc-aea3-708442d4072a", "question_number": 10, "question": "Registration / License numbers provided by the client are not recorded in report legal section or special note", "summary": "No License or Registration numbers found in report sections.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "77687e0e-2b39-456b-9822-1c21b41c46e8", "question_number": 11, "question": "Biased language, Colloquial language,", "summary": "PaymentsSection contains biased language: 'Large' and 'Average' are subjective.", "confidence_score": 0.9, "relevant_sections": ["Report/PaymentsSection/CreditOpinion", "Report/PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "40749d7e-38ba-482c-8da2-2eb93a266db2", "question_number": 12, "question": "Spelling Check", "summary": "Spelling error found: 'testt' in SourceDescription of Non Current Assets.", "confidence_score": 0.9, "relevant_sections": ["FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem/SourceDescription"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "6dbc3b44-f9cb-4b69-8359-c7cfc2dfa6ba", "question_number": 13, "question": "Adverse announcements are included in significant changes but the payments does not make any reference to the significant change.", "summary": "Payments section lacks reference to significant changes, violates compliance.", "confidence_score": 0.9, "relevant_sections": ["PaymentsSection/CreditOpinion"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "32b45431-3ca3-4b10-9a22-8467c03ef548", "question_number": 14, "question": "Adverse announcements are included in significant changes and also payments sections the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Trade Risk Assessment is 'High', violates expected outcome of NoCredit.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/TradeRiskAssessment", "PaymentsSection/CreditOpinion", "PaymentsSection/MaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "19df59c4-1b11-4d59-ade6-972b6bb26cad", "question_number": 15, "question": "Adverse announcements should be a negative description", "summary": "Adverse announcements not present; no negative descriptions found.", "confidence_score": 0.9, "relevant_sections": ["Report/FinancialSection/BalanceSheets/BalanceSheet/LineItems/LineItem"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "6ab108c8-d4da-42c8-961f-72dddca94dbc", "question_number": 16, "question": "Payments sections does not contain relevant information from the Subject related to the sector that the company operates in", "summary": "Payments section lacks sector-related info; violates compliance.", "confidence_score": 0.9, "relevant_sections": ["PaymentsSection/CreditOpinion", "FinancialSection/BalanceSheetFigures/CompanyName"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "bf627e41-76da-495c-a1ab-b8ca5441aa13", "question_number": 17, "question": "Services activities, incorrect texts added re export", "summary": "Incorrect text 'test' added in AdditionalNoteMaxCredit, violates export activity rule.", "confidence_score": 0.9, "relevant_sections": ["PaymentsSection/AdditionalNoteMaxCredit"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "177e205d-7f4b-49ee-816c-3a5f0bf32bb0", "question_number": 18, "question": "If Income is populated Total Income should be also populated or if Total Income is populated Income should also be populated", "summary": "Income and Total Income are both missing, cannot verify compliance.", "confidence_score": 0.9, "relevant_sections": ["Financial/ProfitAndLoss/Income", "Financial/ProfitAndLoss/TotalIncome"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "92711fd5-8044-4d2e-b371-738357da2db9", "question_number": 19, "question": "Credit opinion dif currency requested, no credit given, TRA high", "summary": "Trade Risk Assessment is 'Average', violates TRA high requirement.", "confidence_score": 0.95, "relevant_sections": ["PaymentsSection/CreditOpinion", "PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "522fde43-5143-4cc5-8756-eebd10090c3c", "question_number": 20, "question": "Country risk classification added when not applicable", "summary": "Country risk classification not applicable; 'Average' found in TradeRiskAssessment.", "confidence_score": 0.9, "relevant_sections": ["PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "49fee1f5-56f3-4eb0-98de-cc58fe4fe24b", "question_number": 21, "question": "TRA no classification for dormant, ceased trading", "summary": "Trade Risk Assessment is 'Average', no dormant/ceased classification found.", "confidence_score": 0.9, "relevant_sections": ["Report/PaymentsSection/TradeRiskAssessment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "564c8288-f4ce-421a-bf8d-34120dad2a31", "question_number": 22, "question": "License number provided in the order is for a Registered branch. The license number provided is not listed within the Related entities section as a Branch and a special note has not been added and Transfer to Principal has not been selected", "summary": "License number 'Not Listed' violates branch requirement; no special note or transfer selected.", "confidence_score": 0.98, "relevant_sections": ["RelatedEntities/Entity/LicenseNumber", "RelatedEntities/Entity/SpecialNote", "RelatedEntities/Entity/TransferToPrincipal"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "4b7c9137-7eed-45e5-90e7-aa7ccbc11a30", "question_number": 23, "question": "If any registiration number has expired  a note should be added to Comment explaining the expiry", "summary": "Comment present: 'Registration number has expired.' but no registration number found.", "confidence_score": 0.85, "relevant_sections": ["Report/Comments/Comment"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "1653a1f3-30ff-4b58-aa08-ebe08ad4eb7a", "question_number": 24, "question": "Small with large amount", "summary": "Max Credit '2321' is less than Credit Figure '2122', violates small with large rule", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/MaxCredit", "PaymentsSection/CreditFigure"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "1c8d1b7e-d0b8-4059-923c-d5cf51cb607d", "question_number": 25, "question": "Large with small amount", "summary": "Max Credit '2321' exceeds Credit Figure '2122', violates large with small amount rule", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/CreditOpinion", "PaymentsSection/MaxCredit", "PaymentsSection/CreditFigure"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "fde58506-9f69-4301-b062-1bab02ee3f69", "question_number": 26, "question": "Position classifications should be similar position for", "summary": "Position classifications not present; cannot verify compliance.", "confidence_score": 0.85, "relevant_sections": ["Report/FinancialSection/BalanceSheets"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "8dfa543d-79e4-4678-9557-da5ffbd6a617", "question_number": 27, "question": "The research type is Negative , reports should have Header, Address and general info and special notes reflecting that the company requests could notbe located", "summary": "Header missing 'Research Type' and 'Address', violates validation rule.", "confidence_score": 0.9, "relevant_sections": ["Report/HeaderSection", "Report/GeneralInfo/SpecialNotes"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "c50ca18b-2126-49ec-a1e3-e64e3d6e1366", "question_number": 28, "question": "The research type is No Contact and means the subject company has not been contacted and we have not been able to confirm with anyone within the subject company and the Person Interviewed should not be present", "summary": "Research Type is 'Decline', Person Interviewed not present, compliant.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/PaymentsSection/PaymentHistory"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "135aa79a-7493-4180-85a6-7ddcd2250a4d", "question_number": 29, "question": "The research type is Decline and means the subject company has been contacted and the company has declined to give any information, the payments section should have a comment stating the company has declined to give information", "summary": "Research Type is 'Decline', Comment confirms company declined info, compliant.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/Comment", "Header/ResearchType"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "5eabaf95-3d9c-45e2-b294-7fa88ecda941", "question_number": 30, "question": "The research type is blank and means the subject company has been contacted, the payments section should have a comment stating the company has been contacted and the Person Interviewed should be present", "summary": "Research Type is blank, but Person Interviewed is present. Compliance violated.", "confidence_score": 0.9, "relevant_sections": ["Report/PaymentsSection/AdditionalNoteMaxCredit", "Report/PersonInterviewed/Name"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "b87e9aec-b2c5-4dcc-bc49-dde98cb5002a", "question_number": 31, "question": "Sanctions search should always be performed", "summary": "Sanctions search not performed; missing relevant section in XML.", "confidence_score": 0.98, "relevant_sections": ["Report/PaymentsSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "b2923b6b-9eae-41e6-9281-052b9ac47c30", "question_number": 32, "question": "Sanctions are present  the payments sections should state this, the trade risk assessment should High and Credit opinion should be blank, Max Credit should be blank and Opinion on Max Credit", "summary": "Sanction not found; Max Credit 'AMD' violates blank requirement.", "confidence_score": 0.9, "relevant_sections": ["PaymentsSection/CreditOpinion", "PaymentsSection/MaxCredit", "PaymentsSection/TradeRiskAssessment"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "57c3bfe3-337f-40f4-9a9c-c11e2baabfc4", "question_number": 33, "question": "Town names should not be present within the Registration Number Value", "summary": "Question skipped - Client code mismatch. Question is for client 'TURKEXIM' but current report is for client '0447'.", "confidence_score": 0.0, "relevant_sections": [], "status": "skipped", "client_match_status": "skipped"}, {"question_id": "60401575-d8fe-4f2c-bed8-c75fd7b9f87f", "question_number": 34, "question": "Provenenace icon is missing when data is added to the section", "summary": "Provenance icon missing in PaymentsSection and FinancialSection.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection", "FinancialSection"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "568ca36f-50ae-41cc-b573-bc4d6a84dd8f", "question_number": 35, "question": "Research type select but no telephone number or email address provided in the report", "summary": "No contact details found; violates requirement for 'Select' research type.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/Address"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "39aeb8af-8db0-4867-9300-ef20b5e17946", "question_number": 36, "question": "Capital missing currency", "summary": "Currency fields present: MaxCreditCurrency 'AMD', CreditFigureCurrency 'AMD', BalanceSheet 'AUD'", "confidence_score": 0.9, "relevant_sections": ["PaymentsSection/MaxCreditCurrency", "PaymentsSection/CreditFigureCurrency", "FinancialSection/BalanceSheets/BalanceSheet/Currency"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "17047500-d531-43e3-a635-172d25b81e3f", "question_number": 37, "question": "Member of a group with related entities and Shareholder is not populated", "summary": "Shareholder information is missing, violates related entities requirement.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/Shareholder"], "status": "rejected", "client_match_status": "no_client_code"}, {"question_id": "e109e37f-bd4f-474f-8f0f-de98ca4a8946", "question_number": 38, "question": "Calculate credit opinion", "summary": "Credit Figure '2122' is less than Max Credit '2321', compliant with opinion.", "confidence_score": 0.98, "relevant_sections": ["PaymentsSection/CreditFigure", "PaymentsSection/MaxCredit"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "e1283666-4fdb-4fa9-a2b9-f9ba697c4eaf", "question_number": 39, "question": "Name in financials not matching name in the header when Subject is selected", "summary": "Company name 'AishwaryaTest' matches header name 'AishwaryaTest'", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection/CompanyName", "Report/FinancialSection/BalanceSheetFigures/CompanyName"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}, {"question_id": "2356c19b-2f9b-47a3-8ad6-60ae80912a1e", "question_number": 40, "question": "Town missing in town box", "summary": "Town box is empty, violates requirement for address completeness.", "confidence_score": 0.98, "relevant_sections": ["Report/HeaderSection", "Report/Address"], "status": "approved", "client_match_status": "no_client_code"}, {"question_id": "a4576c9e-5d7f-488f-98c9-4e8e31e83c32", "question_number": 41, "question": "If Company status in legal section not known, credit should not be granted", "summary": "Company status not known; credit should not be granted.", "confidence_score": 0.98, "relevant_sections": ["Report/PaymentsSection/CreditOpinion", "Report/FinancialSection/BalanceSheetFigures"], "status": "manual_intervention_needed", "client_match_status": "no_client_code"}], "validation_timestamp": "2025-07-15 20:09:22.484191", "processing_time": 90.**************, "validation_options": {}, "client_filtering_enabled": true, "order_client_code": "0447", "rag_enabled": true, "permanent_question_bank_used": true}