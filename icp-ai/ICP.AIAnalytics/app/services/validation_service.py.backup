from langchain_openai import Chat<PERSON>penAI
from langchain.chains import <PERSON><PERSON><PERSON><PERSON>
from langchain.prompts import PromptTemplate
from langchain.schema import SystemMessage, HumanMessage
from typing import List, Dict, Any, Optional
import asyncio
import json
import uuid
from datetime import datetime
import time
import xml.etree.ElementTree as ET

from app.core.config import settings
from app.models.schemas import ValidationResult, ValidationStatus, Question
from app.services.file_processor import FileProcessor


class ValidationService:
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            openai_api_key=settings.OPENAI_API_KEY,
            temperature=0.3,
            max_tokens=settings.MAX_TOKENS
        ) if settings.OPENAI_API_KEY else None
        
        self.file_processor = FileProcessor()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Track processed summaries to ensure variety
        self.processed_summaries = set()
        
        # Enhanced validation prompt template
        self.validation_prompt = PromptTemplate(
            input_variables=["question", "xml_content", "question_number"],
            template="""
You are an expert XML financial and business report analyst. Your task is to provide precise, factual answers to specific questions about the XML report content and determine if the findings are positive or negative.

QUESTION #{question_number}: {question}

XML REPORT CONTENT:
{xml_content}

CRITICAL INSTRUCTIONS:
1. ANALYZE the question carefully - if it's vague (like "Client", "All", "ALL"), interpret it contextually based on the XML content
2. PROVIDE SPECIFIC FACTUAL INFORMATION from the XML content, not generic descriptions
3. EXTRACT exact values, names, numbers, dates, and specific details from the XML
4. If the question is "Client" - look for client names, company names, or customer information
5. If the question is "All" or "ALL" - provide a comprehensive summary of all relevant information found
6. CRITICAL: Each answer must be UNIQUE and DIFFERENT from other questions - avoid repeating the same information
7. NEVER include JSON formatting in your summary - provide clean, readable text only
8. Use specific data points and exact values from the XML content
9. Reference multiple different sections when available to provide comprehensive answers
10. For similar questions, focus on DIFFERENT aspects or provide DIFFERENT levels of detail
11. If you see similar content, extract DIFFERENT specific details for each question
12. DETERMINE STATUS: Assess the findings and assign one of three statuses:
    - "approved": Information is clear, complete, and indicates positive/satisfactory results
    - "rejected": Information is clear, complete, and indicates negative/unsatisfactory results  
    - "manual_intervention_needed": Information is insufficient, unclear, ambiguous, or the question is too vague to provide a definitive answer
    
    Guidelines for status assignment:
    - For compliance questions: "approved" if compliant, "rejected" if non-compliant, "manual_intervention_needed" if compliance status unclear
    - For quality questions: "approved" if good quality, "rejected" if poor quality, "manual_intervention_needed" if quality cannot be determined
    - For error/issue questions: "approved" if no issues found, "rejected" if issues exist, "manual_intervention_needed" if insufficient data to assess
    - For financial health: "approved" if healthy metrics, "rejected" if concerning metrics, "manual_intervention_needed" if incomplete financial data
    - For completeness: "approved" if complete, "rejected" if incomplete, "manual_intervention_needed" if completeness cannot be assessed
    - For general information: "approved" if comprehensive info available, "rejected" if negative findings, "manual_intervention_needed" if vague question or insufficient context

RESPONSE FORMAT - Respond ONLY with a JSON object:
{{
    "summary": "Specific factual answer with exact data from the XML content (no JSON formatting here, just clean text)",
    "confidence_score": 0.95,
    "relevant_sections": ["path1", "path2", "path3"],
    "status": "approved, rejected, or manual_intervention_needed based on the assessment",
    "reasoning": "Brief explanation of your analysis approach and why this status was chosen"
}}

EXAMPLE RESPONSES:
- For data quality with clear issues: {{"summary": "125 validation errors found including 1250 missing email addresses", "status": "rejected", "reasoning": "Clear data quality issues identified"}}
- For successful processing: {{"summary": "248,750 of 250,000 records processed successfully (99.5% completion)", "status": "approved", "reasoning": "High completion rate indicates successful processing"}}
- For compliance check with issues: {{"summary": "2 out of 45 business rules failed validation", "status": "rejected", "reasoning": "Business rule failures indicate non-compliance"}}
- For client information found: {{"summary": "Primary client is ABC Corporation Ltd, registered in London", "status": "approved", "reasoning": "Client information successfully identified"}}
- For insufficient data: {{"summary": "Limited client information available - only partial company name visible", "status": "manual_intervention_needed", "reasoning": "Insufficient data to provide comprehensive client details"}}
- For vague question: {{"summary": "Question requires clarification - 'quality' could refer to data quality, processing quality, or output quality", "status": "manual_intervention_needed", "reasoning": "Question too vague to provide specific answer"}}

REMEMBER: Each question should have a UNIQUE answer focusing on different aspects of the data, with appropriate approved/rejected/manual_intervention_needed status.
"""
        )

    def _initialize_vector_store(self):
        """Initialize vector store connection."""
        try:
            from app.services.vector_store import VectorStore
            print("Initializing vector store connection...")
            vector_store = VectorStore()
            # (Suppress all connection status/warning prints)
            return vector_store
        except Exception as e:
            # Only print the initialization error if vector store cannot be created at all
            print(f"Vector store initialization warning: {e}")
            return None

    async def validate_report(self, report_file_id: str, validation_options: Dict[str, Any] = None, enable_client_filtering: bool = False, order_details_params: Dict[str, Any] = None, direct_client_code: str = None) -> Dict[str, Any]:
        """Validate XML report against questions and return results."""
        try:
            validation_id = str(uuid.uuid4())
            start_time = time.time()
            
            # Always use permanent question bank
            print("Using permanent question bank")
            questions = await self.file_processor.load_permanent_questions(force_reload=True)
            questions_source = "permanent_question_bank"
            actual_questions_file_id = "permanent"
            
            # Load report
            report_data = await self.file_processor.get_processed_report(report_file_id)
            
            if not questions:
                raise Exception("Permanent question bank is enabled but no questions found. Please upload a permanent question bank first.")
            
            if not report_data:
                raise Exception("Report not found or not processed")
            
            print(f"Loaded {len(questions)} questions from {questions_source}")
            
            # Connect to vector store 
            if self.vector_store:
                print("Connecting to vector store ...")
                # Test vector store connection
                await self._vector_store_operations(report_data, questions)
            
            # Handle client filtering if enabled
            order_client_code = None
            # Don't filter questions - process all and mark as skipped if needed
            filtered_questions = questions
            skipped_questions = 0
            
                        if enable_client_filtering:
                if question.client_code:
                    # Question has a client code (client-specific question)
                    if order_client_code:
                        # We have both question client code and order client code - compare them
                        if question.client_code.upper() != order_client_code.upper():
                            # Question is for a different client - mark as skipped
                            client_match_status = "skipped"
                            enhanced_question_text = self._build_enhanced_question(question)
                            return ValidationResult(
                                question_id=question.id,
                                question_number=question_number,
                                question=enhanced_question_text,
                                summary=f"Question skipped - Client code mismatch. Question is for client '{question.client_code}' but current report is for client '{order_client_code}'.",
                                confidence_score=0.0,
                                relevant_sections=[],
                                status="skipped",
                                client_match_status="skipped"
                            )
                        else:
                            # Question matches client code
                            client_match_status = "matched"
                    else:
                        # Client filtering enabled but no order client code provided - skip client-specific questions
                        client_match_status = "skipped"
                        enhanced_question_text = self._build_enhanced_question(question)
                        return ValidationResult(
                            question_id=question.id,
                            question_number=question_number,
                            question=enhanced_question_text,
                            summary=f"Question skipped - Client filtering is enabled but no client code provided for this validation. Question is for client '{question.client_code}'.",
                            confidence_score=0.0,
                            relevant_sections=[],
                            status="skipped",
                            client_match_status="skipped"
                        )
                else:
                    # Question has no client code - applies to all clients
                    client_match_status = "no_client_code"            
            if enable_client_filtering:
                if question.client_code:
                    # Question has a client code (client-specific question)
                    if order_client_code:
                        # We have both question client code and order client code - compare them
                            if question.client_code.upper() != order_client_code.upper():
                                # Question is for a different client - mark as skipped
                                client_match_status = "skipped"
                                enhanced_question_text = self._build_enhanced_question(question)
                                enhanced_question_text = self._build_enhanced_question(question)
                                return ValidationResult(
                                question_id=question.id,
                                question_number=question_number,
                                question=enhanced_question_text,
                                summary=f"Question skipped - Client code mismatch. Question is for client '{question.client_code}' but current report is for client '{order_client_code}'.",
                                confidence_score=0.0,
                                relevant_sections=[],
                                status="skipped",
                                client_match_status="skipped"
                                )
                        else:
                            # Question matches client code
                            client_match_status = "matched"
                        # Question matches client code
                        client_match_status = "matched"
                else:
                    # Client filtering enabled but no order client code provided - skip client-specific questions
                    client_match_status = "skipped"
                    enhanced_question_text = self._build_enhanced_question(question)
                    return ValidationResult(
                        question_id=question.id,
                        question_number=question_number,
                        question=enhanced_question_text,
                        summary=f"Question skipped - Client filtering is enabled but no client code provided for this validation. Question is for client '{question.client_code}'.",
                        confidence_score=0.0,
                        relevant_sections=[],
                        status="skipped",
                        client_match_status="skipped"
                    )
            else:
                # Question has no client code - applies to all clients
                client_match_status = "no_client_code"
            
            # Extract relevant XML content for this question
            xml_content = await self._extract_xml_content_for_question(report_data, question.question)
            
            # Enhanced validation with new fields
            enhanced_question_text = self._build_enhanced_question(question)
            
            if self.llm:
                # Use LLM for validation
                response = await self._generate_enhanced_llm_response(question, xml_content, question_number)
            else:
                # Fallback to rule-based validation
                response = self._generate_fallback_response(question, xml_content)
            
            # Parse response
            parsed_response = self._parse_llm_response(response)
            
            # Check for duplicate summary
            is_duplicate = self._check_for_duplicate_summary(parsed_response["summary"], question.question)
            if is_duplicate:
                # Regenerate response
                retry_response = await self._generate_enhanced_llm_response(question, xml_content, question_number, retry=True)
                parsed_response = self._parse_llm_response(retry_response)
            
            # Add summary to tracking
            self._add_summary_to_tracked(parsed_response["summary"])
            
            # Determine status
            status = self._determine_status_from_summary(parsed_response["summary"], question.question)
            
            return ValidationResult(
                question_id=question.id,
                question_number=question_number,
                question=enhanced_question_text,
                summary=parsed_response["summary"],
                confidence_score=parsed_response["confidence_score"],
                relevant_sections=parsed_response["relevant_sections"],
                status=status,
                client_match_status=client_match_status
            )
            
        except Exception as e:
            enhanced_question_text = self._build_enhanced_question(question)
            return ValidationResult(
                question_id=question.id,
                question_number=question_number,
                question=enhanced_question_text,
                summary=f"Error validating question: {str(e)}",
                confidence_score=0.0,
                relevant_sections=[],
                status="error",
                client_match_status=client_match_status if 'client_match_status' in locals() else "no_client_code"
            )

    async def _extract_xml_content_for_question(self, report_data: Dict[str, Any], question: str) -> str:
        """Extract relevant XML content for a specific question."""
        try:
            # Use LLM-based semantic search if LLM is available
            if self.llm:
                return await self._llm_semantic_extract_xml_content(report_data, question)
            else:
                # Fallback to direct extraction
                return self._direct_extract_xml_content(report_data, question)
            
        except Exception as e:
            return f"Error extracting XML content: {str(e)}"

    async def _llm_semantic_extract_xml_content(self, report_data: Dict[str, Any], question: str) -> str:
        """Use LLM to semantically extract relevant XML sections."""
        try:
            # Check if LLM semantic search is enabled
            if not settings.ENABLE_LLM_SEMANTIC_SEARCH or not self.llm:
                return self._direct_extract_xml_content(report_data, question)
            
            # Get the XML structure and text content
            xml_structure = report_data.get("xml_structure", {})
            text_content = report_data.get("text_content", [])
            
            # Prepare content for semantic analysis
            xml_str = self._dict_to_xml_string(xml_structure)
            
            # Create semantic search prompt with increased content limit
            content_sections = []
            for item in text_content[:50]:  # Increased limit for better semantic search
                if item.get("text"):
                    content_sections.append(f"Path: {item['path']}\nContent: {item['text']}")
            
            full_content = f"XML Structure:\n{xml_str}\n\nText Content:\n" + "\n\n".join(content_sections)
            
            # Use configured content limit for semantic search
            if len(full_content) > settings.SEMANTIC_SEARCH_CONTENT_LIMIT:
                return await self._llm_extract_relevant_sections(full_content, question)
            else:
                return full_content[:settings.SEMANTIC_SEARCH_CHUNK_SIZE]
                
        except Exception as e:
            # Fallback to direct extraction
            print(f"LLM semantic search failed: {e}")
            return self._direct_extract_xml_content(report_data, question)

    async def _llm_extract_relevant_sections(self, full_content: str, question: str) -> str:
        """Use LLM to extract only the most relevant sections from large XML content."""
        try:
            # Split content into manageable chunks using configured size
            chunks = self._split_content_into_chunks(full_content, settings.SEMANTIC_SEARCH_CHUNK_SIZE)
            
            # Enhanced semantic extraction prompt
            extraction_prompt = f"""
You are an expert semantic content extraction assistant specializing in XML document analysis. 

QUESTION: {question}

ANALYSIS INSTRUCTIONS:
1. Identify XML sections that are directly relevant to answering the question
2. Look for semantic relationships, not just keyword matches
3. Include context sections that provide supporting information
4. Consider business logic and domain-specific meanings
5. Exclude irrelevant sections to keep the response concise
6. Preserve the original XML structure and paths
7. Focus on content that would help answer the specific question asked

SEMANTIC MATCHING PRIORITIES:
- Direct content matches (highest priority)
- Contextually related content
- Supporting information and metadata
- Business logic relationships

XML CONTENT:
{chunks[0]}

Return only the extracted relevant content with preserved XML structure.
"""
            
            # Use LLM for semantic extraction
            messages = [
                SystemMessage(content="You are an expert semantic content extraction assistant. Extract only the most relevant XML sections for the given question using semantic understanding. Preserve original structure and paths."),
                HumanMessage(content=extraction_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            extracted_content = response.content
            
            # Process additional chunks if needed and configured
            if len(chunks) > 1 and len(chunks) <= settings.SEMANTIC_SEARCH_MAX_CHUNKS:
                # Process additional chunks if the first extraction seems insufficient
                if len(extracted_content) < settings.SEMANTIC_SEARCH_MIN_EXTRACTION_SIZE:
                    for chunk in chunks[1:settings.SEMANTIC_SEARCH_MAX_CHUNKS]:
                        additional_prompt = f"""
Continue semantic extraction for the question: {question}

Additional XML content to analyze:
{chunk}

SEMANTIC ANALYSIS REQUIREMENTS:
- Only include sections that add new relevant information not already covered
- Maintain semantic coherence with previously extracted content
- Focus on complementary information that enhances understanding
- Preserve XML structure and context

Return only new relevant content that complements the previous extraction.
"""
                        
                        additional_messages = [
                            SystemMessage(content="Continue semantic extraction. Only include new relevant information that complements previous extraction."),
                            HumanMessage(content=additional_prompt)
                        ]
                        
                        additional_response = await self.llm.ainvoke(additional_messages)
                        extracted_content += "\n\n" + additional_response.content
                        
                        if len(extracted_content) > settings.SEMANTIC_SEARCH_CHUNK_SIZE:
                            break
            
            return extracted_content[:settings.SEMANTIC_SEARCH_CHUNK_SIZE]
            
        except Exception as e:
            # Fallback to truncated content
            print(f"LLM semantic section extraction failed: {e}")
            return full_content[:settings.SEMANTIC_SEARCH_CHUNK_SIZE]

    def _split_content_into_chunks(self, content: str, chunk_size: int) -> List[str]:
        """Split content into chunks while trying to preserve XML structure."""
        chunks = []
        
        # First try to split by XML sections (double newlines)
        sections = content.split('\n\n')
        
        current_chunk = ""
        for section in sections:
            if len(current_chunk) + len(section) + 2 <= chunk_size:
                current_chunk += section + "\n\n"
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = section + "\n\n"
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        # If chunks are still too large, split by lines
        if chunks and max(len(chunk) for chunk in chunks) > chunk_size:
            refined_chunks = []
            for chunk in chunks:
                if len(chunk) <= chunk_size:
                    refined_chunks.append(chunk)
                else:
                    # Split large chunk by lines
                    lines = chunk.split('\n')
                    current_line_chunk = ""
                    for line in lines:
                        if len(current_line_chunk) + len(line) + 1 <= chunk_size:
                            current_line_chunk += line + "\n"
                        else:
                            if current_line_chunk:
                                refined_chunks.append(current_line_chunk.strip())
                            current_line_chunk = line + "\n"
                    if current_line_chunk:
                        refined_chunks.append(current_line_chunk.strip())
            chunks = refined_chunks
        
        return chunks

    def _direct_extract_xml_content(self, report_data: Dict[str, Any], question: str) -> str:
        """Direct extraction method (original implementation)."""
        try:
            # Get the XML structure and text content
            xml_structure = report_data.get("xml_structure", {})
            text_content = report_data.get("text_content", [])
            
            # Convert XML structure to string representation
            xml_str = self._dict_to_xml_string(xml_structure)
            
            # Add text content sections
            content_sections = []
            for item in text_content[:10]:  # Limit to first 10 sections for performance
                if item.get("text"):
                    content_sections.append(f"Path: {item['path']}\nContent: {item['text']}")
            
            # Combine all content
            full_content = f"XML Structure:\n{xml_str}\n\nText Content:\n" + "\n\n".join(content_sections)
            
            return full_content[:8000]  # Limit content length for LLM
            
        except Exception as e:
            return f"Error extracting XML content: {str(e)}"

    def _dict_to_xml_string(self, data: Dict[str, Any], indent: int = 0) -> str:
        """Convert dictionary to XML-like string representation."""
        if not isinstance(data, dict):
            return str(data)
        
        result = []
        for key, value in data.items():
            if isinstance(value, dict):
                result.append("  " * indent + f"<{key}>")
                result.append(self._dict_to_xml_string(value, indent + 1))
                result.append("  " * indent + f"</{key}>")
            elif isinstance(value, list):
                for item in value:
                    result.append("  " * indent + f"<{key}>")
                    if isinstance(item, dict):
                        result.append(self._dict_to_xml_string(item, indent + 1))
                    else:
                        result.append("  " * (indent + 1) + str(item))
                    result.append("  " * indent + f"</{key}>")
            else:
                result.append("  " * indent + f"<{key}>{value}</{key}>")
        
        return "\n".join(result)

    def _build_enhanced_question(self, question: Question) -> str:
        """Build enhanced question text with additional context from new fields."""
        enhanced_parts = [question.question]
        
        if question.darwin_reference_sections:
            enhanced_parts.append(f"[Reference Sections: {question.darwin_reference_sections}]")
        
        if question.expected_outcome:
            enhanced_parts.append(f"[Expected Outcome: {question.expected_outcome}]")
        
        if question.client_specific_type:
            enhanced_parts.append(f"[Client Type: {question.client_specific_type}]")
        
        return " ".join(enhanced_parts)

    async def _generate_enhanced_llm_response(self, question: Question, xml_content: str, question_number: int, retry: bool = False) -> str:
        """Generate enhanced LLM response using new question fields for better context."""
        try:
            # Build enhanced prompt with additional context
            enhanced_prompt = self._build_enhanced_validation_prompt(question, xml_content, question_number, retry)
            
            # Enhanced system message for better context understanding
            system_prompt = """You are an expert financial and business report analyst specializing in XML document analysis.

Your expertise includes:
- Financial statements analysis (balance sheets, P&L, cash flow)
- Corporate structure and governance
- Legal and regulatory compliance
- Business entity information
- Professional service providers (lawyers, accountants)
- Related entities and subsidiaries

Key principles:
1. Extract EXACT values, names, numbers, and dates from the XML content
2. Use the Darwin reference sections to focus your analysis on the right areas
3. Align your findings with the expected outcome when provided
4. Provide specific, factual information rather than generic descriptions
5. Each response must be unique and tailored to the specific question
6. Always respond with valid JSON format only
7. Be precise and comprehensive in your analysis

Focus on delivering actionable intelligence from the XML data while considering the provided reference sections and expected outcomes."""
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=enhanced_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            return response.content
            
        except Exception as e:
            raise Exception(f"Error generating enhanced LLM response: {str(e)}")

    def _build_enhanced_validation_prompt(self, question: Question, xml_content: str, question_number: int, retry: bool = False) -> str:
        """Build enhanced validation prompt incorporating new question fields."""
        
        base_prompt = f"""Analyze the following XML content to answer question #{question_number}:

QUESTION: {question.question}"""
        
        # Add Darwin reference sections if available
        if question.darwin_reference_sections:
            base_prompt += f"""

FOCUS AREAS (Darwin Reference Sections): {question.darwin_reference_sections}
Please pay special attention to these sections in the XML content when analyzing."""
        
        # Add expected outcome if available
        if question.expected_outcome:
            base_prompt += f"""

EXPECTED OUTCOME: {question.expected_outcome}
Your analysis should align with this expected outcome and validate whether it is met."""
        
        # Add client-specific information if available
        if question.client_specific_type:
            base_prompt += f"""

CLIENT SCOPE: {question.client_specific_type}
Consider the client-specific nature of this validation."""
        
        # Add retry instruction if this is a retry
        if retry:
            base_prompt += """

RETRY INSTRUCTION: This is a retry attempt. Please provide a different analytical approach and avoid duplicating previous responses."""
        
        base_prompt += f"""

XML CONTENT TO ANALYZE:
{xml_content}

RESPONSE REQUIREMENTS:
Please provide your analysis in the following JSON format:
{{
    "summary": "Detailed analysis findings with specific data from XML",
    "confidence_score": 0.0-1.0,
    "relevant_sections": ["list", "of", "relevant", "xml", "sections"],
    "status": "found|not_found|partial|error"
}}

ANALYSIS GUIDELINES:
1. Extract EXACT values, names, numbers, and dates from the XML
2. If Darwin reference sections were provided, focus your analysis on those areas
3. If an expected outcome was specified, validate whether it is achieved
4. Provide specific, factual findings rather than generic statements
5. Include confidence score based on clarity and completeness of findings
6. List the specific XML sections that supported your analysis
7. Ensure each response is unique and directly addresses the question"""
        
        return base_prompt

    async def _generate_llm_response(self, question: str, xml_content: str, question_number: int) -> str:
        """Generate response using LLM."""
        try:
            prompt = self.validation_prompt.format(
                question=question,
                xml_content=xml_content,
                question_number=question_number
            )
            
            # Enhanced system message for better context understanding
            system_prompt = """You are an expert financial and business report analyst specializing in XML document analysis.

Your expertise includes:
- Financial statements analysis (balance sheets, P&L, cash flow)
- Corporate structure and governance
- Legal and regulatory compliance
- Business entity information
- Professional service providers (lawyers, accountants)
- Related entities and subsidiaries

Key principles:
1. Extract EXACT values, names, numbers, and dates from the XML content
2. Provide specific, factual information rather than generic descriptions
3. Each response must be unique and tailored to the specific question
4. Always respond with valid JSON format only
5. Be precise and comprehensive in your analysis

Focus on delivering actionable intelligence from the XML data."""
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            return response.content
            
        except Exception as e:
            raise Exception(f"Error generating LLM response: {str(e)}")

    def _generate_fallback_response(self, question: Question, xml_content: str) -> str:
        """Generate fallback response without LLM."""
        if not xml_content:
            summary = "No XML content available for analysis."
            return json.dumps({
                "summary": summary,
                "confidence_score": 0.1,
                "relevant_sections": [],
                "status": self._determine_status_from_summary(summary, question.question),
                "reasoning": "No LLM available and no XML content found"
            })
        
        # Simple keyword matching
        summary = f"Based on the available XML content: {xml_content[:200]}..."
        
        return json.dumps({
            "summary": summary,
            "confidence_score": 0.5,
            "relevant_sections": [],
            "status": self._determine_status_from_summary(summary, question.question),
            "reasoning": "Generated using fallback method without LLM"
        })

    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        """Parse LLM response to extract structured data and clean up formatting."""
        try:
            # Clean up the response first
            clean_response = response.strip()
            
            # Remove markdown code blocks if present
            if clean_response.startswith("```json"):
                clean_response = clean_response.replace("```json", "").replace("```", "").strip()
            elif clean_response.startswith("```"):
                clean_response = clean_response.replace("```", "").strip()
            
            # Try to parse as JSON
            parsed = json.loads(clean_response)
            
            # Clean up the summary field to remove any JSON artifacts
            if "summary" in parsed:
                summary = parsed["summary"].strip()
                # Remove any nested JSON formatting in the summary
                if summary.startswith('```json') or summary.startswith('{'):
                    # If summary contains JSON, try to extract just the summary value
                    try:
                        if summary.startswith('```json'):
                            json_part = summary.replace('```json', '').replace('```', '').strip()
                            nested_json = json.loads(json_part)
                            summary = nested_json.get("summary", summary)
                        elif summary.startswith('{'):
                            nested_json = json.loads(summary)
                            summary = nested_json.get("summary", summary)
                    except:
                        # If parsing fails, keep original but clean it
                        summary = summary.replace('```json', '').replace('```', '').replace('{', '').replace('}', '').strip()
                
                parsed["summary"] = summary
            
            # Ensure we have 'summary' field for backward compatibility
            if "answer" in parsed and "summary" not in parsed:
                parsed["summary"] = parsed["answer"]
                
            return parsed
            
        except json.JSONDecodeError:
            # Fallback parsing
            clean_text = response.replace('```json', '').replace('```', '').strip()
            return {
                "summary": clean_text,
                "confidence_score": 0.5,
                "relevant_sections": [],
                "reasoning": "Unable to parse structured response"
            }

    async def _save_validation_results(self, validation_id: str, validation_data: Dict[str, Any]):
        """Save validation results to file."""
        try:
            results_path = self.file_processor.processed_path / f"{validation_id}_validation.json"
            
            # Convert datetime objects to strings for JSON serialization
            serializable_data = json.loads(json.dumps(validation_data, default=str))
            
            with open(results_path, "w") as f:
                json.dump(serializable_data, f, indent=2)
                
        except Exception as e:
            print(f"Error saving validation results: {str(e)}")

    async def get_validation_results(self, validation_id: str) -> Optional[Dict[str, Any]]:
        """Get validation results by ID."""
        try:
            results_path = self.file_processor.processed_path / f"{validation_id}_validation.json"
            if results_path.exists():
                with open(results_path, "r") as f:
                    return json.load(f)
            return None
        except Exception:
            return None 

    def _check_for_duplicate_summary(self, summary: str, question_text: str) -> bool:
        """Check if a summary is too similar to previously processed summaries."""
        if not summary or len(summary) < 10:
            return False
            
        # Normalize the summary for comparison
        normalized_summary = summary.lower().strip()
        
        # Check against previously processed summaries
        for existing_summary in self.processed_summaries:
            if self._calculate_similarity(normalized_summary, existing_summary) > 0.8:
                return True
        
        return False
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two texts using simple word overlap."""
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _add_summary_to_tracked(self, summary: str):
        """Add a summary to the tracked set."""
        if summary:
            self.processed_summaries.add(summary.lower().strip())

    def _determine_status_from_summary(self, summary: str, question: str) -> str:
        """Determine status based on summary content if not provided by LLM."""
        if not summary:
            return "manual_intervention_needed"
        
        summary_lower = summary.lower()
        question_lower = question.lower()
        
        # Keywords that indicate insufficient data or vague questions
        insufficient_data_indicators = [
            "limited", "partial", "insufficient", "unclear", "ambiguous", "vague",
            "cannot determine", "unable to determine", "not enough", "not sufficient",
            "requires clarification", "needs more information", "incomplete data",
            "insufficient information", "no clear", "not clear", "unclear from",
            "missing context", "lacks detail", "too vague", "more specific",
            "clarification needed", "additional information needed"
        ]
        
        # Keywords that typically indicate negative/rejected status
        negative_indicators = [
            "error", "errors", "failed", "failure", "issue", "issues", "problem", "problems",
            "missing", "incomplete", "invalid", "non-compliant", "violation", "violations", 
            "warning", "warnings", "rejected", "declined", "denied", "insufficient",
            "poor", "low quality", "bad", "negative", "concern", "concerning", "risk",
            "duplicate", "duplicates", "discrepancy", "discrepancies", "mismatch"
        ]
        
        # Keywords that typically indicate positive/approved status
        positive_indicators = [
            "successful", "complete", "completed", "compliant", "approved", "valid",
            "good", "excellent", "satisfactory", "acceptable", "confirmed", "verified", 
            "passed", "high quality", "healthy"
        ]
        
        # Check for insufficient data indicators first
        if any(indicator in summary_lower for indicator in insufficient_data_indicators):
            return "manual_intervention_needed"
        
        # Check for truly vague questions (only single generic words with short summaries)
        truly_vague_questions = [
            question_lower.strip() in ["all", "client", "quality", "status", "info", "information"]
        ]
        
        if any(truly_vague_questions) and len(summary_lower) < 30:
            return "manual_intervention_needed"
        
        # Special patterns that override general logic
        # Explicit positive statements (no problems found)
        if any(pattern in summary_lower for pattern in [
            "no error", "no errors", "no issue", "no issues", "no problem", "no problems",
            "no warning", "no warnings", "no duplicate", "no duplicates", "not found",
            "zero errors", "0 errors", "no critical", "no major", "no validation errors"
        ]):
            return "approved"
            
        # Mixed signals - need manual intervention when both positive and negative indicators present
        if any(pattern in summary_lower for pattern in [
            "but overall", "however overall", "overall acceptable", "acceptable overall",
            "but acceptable", "yet acceptable", "some errors found but", "errors found but overall",
            "issues found but", "problems found but", "warnings but", "concerns but"
        ]):
            return "manual_intervention_needed"
        
        # Explicit negative statements
        if any(pattern in summary_lower for pattern in [
            "errors found", "issues found", "problems found", "warnings found",
            "failed validation", "non-compliant", "critical error", "critical errors",
            "major issue", "major issues", "critical", "severe error", "severe errors"
        ]):
            return "rejected"
        
        # Count indicators
        negative_count = sum(1 for indicator in negative_indicators if indicator in summary_lower)
        positive_count = sum(1 for indicator in positive_indicators if indicator in summary_lower)
        insufficient_count = sum(1 for indicator in insufficient_data_indicators if indicator in summary_lower)
        
        # If there are strong insufficient data indicators, prioritize manual intervention
        if insufficient_count > 0:
            return "manual_intervention_needed"
        
        # Special handling for questions asking about errors/issues
        if any(word in question_lower for word in ["error", "issue", "problem", "duplicate", "warning"]):
            # For questions asking about problems:
            # - If no problems mentioned or explicitly "no X" → approved
            # - If problems are mentioned → rejected
            # - If unclear → manual intervention needed
            if any(word in summary_lower for word in negative_indicators):
                # But check if it's negated
                if any(neg in summary_lower for neg in ["no ", "zero ", "0 ", "none", "not "]):
                    return "approved"
                return "rejected"
            return "approved"  # No problems mentioned
        
        # For compliance questions
        if any(word in question_lower for word in ["compliant", "compliance", "rule", "rules"]):
            if any(word in summary_lower for word in ["failed", "non-compliant", "violation"]):
                return "rejected"
            if any(word in summary_lower for word in ["compliant", "passed", "satisfied"]):
                return "approved"
            # If compliance status is unclear
            return "manual_intervention_needed"
        
        # General rule based on indicator counts
        if negative_count > 0 and negative_count > positive_count:
            return "rejected"
        elif positive_count > 0 and positive_count > negative_count:
            return "approved"
        elif negative_count == positive_count and negative_count > 0:
            # Mixed signals, need manual intervention
            return "manual_intervention_needed"
        
        # Default based on overall sentiment
        if any(word in summary_lower for word in ["successfully", "excellent", "good", "high"]):
            return "approved"
        elif any(word in summary_lower for word in ["but", "however", "although"]):
            # Check if the overall conclusion is still positive
            if any(conclusion in summary_lower for conclusion in ["acceptable", "satisfactory", "overall good"]):
                return "approved"
            # Might indicate issues despite positive aspects
            elif negative_count > 0:
                return "rejected"
            else:
                return "manual_intervention_needed"
        
        # If the summary is very short or generic, it might need manual intervention
        if len(summary_lower.split()) < 5:
            return "manual_intervention_needed"
        
        # Default to manual intervention if unclear
        return "manual_intervention_needed"

    async def _fetch_order_client_code(self, order_details_params: Dict[str, Any]) -> Optional[str]:
        """
        Fetch client code from order details API.
        
        Expected API response structure:
        {
            "clientId": 383,
            "clientShortCode": "0447",  # This is the primary client code field
            "clientReference": "tudip1Report",
            "country": "India",
            "companyName": "tudip1",
            ...
        }
        """
        try:
            import httpx
            
            # API configuration
            api_url = f"https://wapaz-uks-uta-icpcertsol-api01.azurewebsites.net/api/order/getOrderDetails?csr={order_details_params['csr_id']}&copy={order_details_params['copy']}&version={order_details_params['version']}"
            headers = {
                "Authorization": f"Bearer {order_details_params['bearer_token']}",
                "Content-Type": "application/json"
            }
            
            # Fetch data from external API
            async with httpx.AsyncClient(timeout=30.0) as client:
                api_response = await client.get(api_url, headers=headers)
                
                if api_response.status_code != 200:
                    print(f"Error fetching order details: {api_response.status_code} - {api_response.text}")
                    return None
                
                # Parse the API response
                try:
                    api_data = api_response.json()
                except Exception as e:
                    print(f"Error parsing order details response: {str(e)}")
                    return None
                
                # Extract client code from response
                client_code = None
                # Updated to prioritize clientShortCode based on API response structure
                possible_client_keys = [
                    'clientShortCode',  # Primary field based on API response
                    'clientCode', 'client_code', 'ClientCode', 'Client_Code',
                    'client', 'Client', 'clientId', 'ClientId', 'client_id'
                ]
                
                # Search for client code in the response
                if isinstance(api_data, dict):
                    for key in possible_client_keys:
                        if key in api_data:
                            client_code = str(api_data[key])
                            break
                    
                    # If not found in root, search nested objects
                    if not client_code:
                        for key, value in api_data.items():
                            if isinstance(value, dict):
                                for nested_key in possible_client_keys:
                                    if nested_key in value:
                                        client_code = str(value[nested_key])
                                        break
                                if client_code:
                                    break
                
                return client_code
                
        except Exception as e:
            print(f"Error fetching order client code: {str(e)}")
            return None
    
    def _filter_questions_by_client(self, questions: List[Question], order_client_code: Optional[str]) -> tuple[List[Question], int]:
        """Filter questions based on client code matching."""
        if not order_client_code:
            # If no order client code, process all questions
            return questions, 0
        
        filtered_questions = []
        skipped_count = 0
        
        for question in questions:
            if question.client_code:
                # If question has client code, only include if it matches
                if question.client_code.strip() == order_client_code.strip():
                    filtered_questions.append(question)
                else:
                    skipped_count += 1
            else:
                # If question has no client code, include it (applies to all clients)
                filtered_questions.append(question)
        
        return filtered_questions, skipped_count 