{"file_id": "819c6b24-ee82-46d8-a710-cb1ea9f0f08e", "xml_structure": {"Report": {"@xmlns:xsd": "http://www.w3.org/2001/XMLSchema", "@xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "HeaderSection": {"Date": "30-Jun-2025", "DeliveryDate": "30-Jun-2025", "OurRef": "1000340.1.1", "YourRef": "1", "CompanyName": "GenAI", "Requested": "GenAI", "ResearcherNotes": "Header checked"}, "PersonnelSection": {"PersonnelNotes": "Test notes", "EmployeeChanges": "Frequently"}, "PaymentsSection": {"PaymentHistory": "PaymentsImport", "QualificationNote": "sadsad<PERSON>d adasdasdsadasdasd23131312312^^^^^^^^^^^^^^^^^^&&&&&&&&&&&&&&&&&&&&", "CreditOpinion": "Small", "CreditOpinionNotes": "&&&&&&&&&&&&&&&&&&&&&&&&&&&&&", "MaxCreditCurrency": "INR", "MaxCredit": "50", "OpinionOnMaxCredit": "NewCompany", "AdditionalNoteMaxCredit": "qewwwwww423333333333))))))))))))))", "TradeRiskAssessment": "No Classification", "CreditFigureCurrency": "INR", "CreditFigure": "5554", "AdditionalNoteCreditFigure": "rwerwrwerwerwer"}, "PublicRecordSection": {"SanctionsSearch": {"SanctionsSearchPerfomed": "true", "SanctionsFound": "true", "SanctionComments": "Sanction test comment", "CompanySanctions": {"CompanySanctionDetail": "test"}, "CountrySanctions": {"CountrySanctionDetail": "asdfsadsad"}}, "JudgementsSearch": {"JudgementsSearchPerfomed": "false", "JudgementsFound": "false", "JudgementComments": "zxffsdfsdfsdfsdfsdfsdfsdfs                                                                                                                                                      sfdsfsdfsdfsdfsdfs                    safddsfsdf             asfdasd"}, "PublicRecordInfo": {"OtherPublicRecordInformation": "dfsdfsdf svdfsdfsdfdsfsdfds     ***************asdasdas asdasdas                                  fsffsdfsdf"}}, "ChangesSection": {"Change": {"ChangeHeading": "SFSDFDS", "ChangeDate": "01-May-2025", "ChangeClassification": "Mergers and Acquisitions", "ChangeDescription": "ASFDSF SFDSFSDF", "ChangeProvenances": {"ChangeProvenance": [{"Provenance": {"ProvenanceID": "1", "ProvenanceName": "Official"}}, {"Provenance": {"ProvenanceID": "4", "ProvenanceName": "ICP"}}, {"Provenance": {"ProvenanceID": "5", "ProvenanceName": "UnOfficial"}}, {"Provenance": {"ProvenanceID": "3", "ProvenanceName": "Research"}}, {"Provenance": {"ProvenanceID": "2", "ProvenanceName": "Subject"}}]}}}, "TradeReferencesSection": {"TradeReference": [{"Name": "AAA", "Formerly": "AAA", "CareOf": "AA", "Building": "AA", "Street": "AA**", "ISO2CountryCode": "IN", "ICPCountryCode": "IND", "ICPCountryName": "India", "InternationalDialingCode": "91", "Number": "SDFSDFDSF", "Extension": "SDFSDFDSF", "Type": "Main", "AccountNumber": "DASDASDASD"}, {"ISO2CountryCode": "IN", "ICPCountryCode": "IND", "ICPCountryName": "India", "InternationalDialingCode": "91"}]}, "BankersSection": {"Bank": {"PrincipalBanker": "true", "Name": "&&&", "AccountTypes": {"Type": "Commercial"}, "Formerly": "*&*", "BranchName": "((((", "Building": "dfsfsdfsd", "Town": "UUU", "ISO2CountryCode": "IN", "ICPCountryCode": "IND", "ICPCountryName": "India", "InternationalDialingCode": "91", "Number": "sdfsdfdsf**"}}, "AccountantsSection": {"Accountant": {"Active": "true", "ServiceTypes": {"Type": "Audit"}, "Building": "No. 7, Bishops Court", "Street": "Corner of Delamore and Warrior Roads", "POBox": "10546, <PERSON><PERSON> 3605", "Town": "Hillcrest", "ProvinceType": "Province", "Province": "KwaZulu-Natal", "PostCode": "3610", "ISO2CountryCode": "ZA", "ICPCountryCode": "SOU", "ICPCountryName": "South Africa", "InternationalDialingCode": "27", "Number": "31 765 3111", "Type": "Main", "Email": "micha<PERSON>@wildnerandcompany.co.za"}}, "CompanySecretarySection": {"Name": "***", "CareOf": "))", "POBox": "&&", "Town": "$$", "ISO2CountryCode": "IN", "ICPCountryCode": "IND", "ICPCountryName": "India", "InternationalDialingCode": "91"}, "LawyersSection": {"Lawyer": {"Active": "true", "Name": "@@@", "ServiceTypes": {"Type": "All Legal Services"}, "Formerly": "!!", "Town": "&&", "ISO2CountryCode": "IN", "ICPCountryCode": "IND", "ICPCountryName": "India", "InternationalDialingCode": "91", "Number": "AAAAAAAAAAAA", "Extension": "BB", "Type": "Main", "Position": "Lawyer"}}, "LegalStatusSection": {"CompanyStatus": "Active", "Capital": {"NotGiven": "false"}, "ICPLegalGroup": "Unlimited Partnership", "LegalStatusCode": "3000", "LegalStatus": "General Partnership"}, "SpecialNotesSection": {"TransferToPrincipal": "false"}, "WaiverSection": {"Waiver": "All information within this report is provided entirely on an 'as is' basis and is used by the recipient at its sole risk. ICP shall use reasonable skill and care in providing information contained within this report and, specifically, in collecting and collating the information from reliable sources and will have no legal responsibility for any error or omission contained within this report or subsequent change to the information. It does not give any warranty or guarantee to information provided or consequences of using the information within this report for decision making purposes.\n\nInformation is to be stored and used in accordance with data protection regulation and will not be supplied, published, displayed, re-sold, copied or made known to the subject without prior agreement from ICP."}}}, "text_content": [{"path": "Report/HeaderSection/Date", "tag": "Date", "text": "30-Jun-2025", "attributes": {}}, {"path": "Report/HeaderSection/DeliveryDate", "tag": "DeliveryDate", "text": "30-Jun-2025", "attributes": {}}, {"path": "Report/HeaderSection/OurRef", "tag": "OurRef", "text": "1000340.1.1", "attributes": {}}, {"path": "Report/HeaderSection/YourRef", "tag": "YourRef", "text": "1", "attributes": {}}, {"path": "Report/HeaderSection/CompanyName", "tag": "CompanyName", "text": "GenAI", "attributes": {}}, {"path": "Report/HeaderSection/Requested", "tag": "Requested", "text": "GenAI", "attributes": {}}, {"path": "Report/HeaderSection/ResearcherNotes", "tag": "ResearcherNotes", "text": "Header checked", "attributes": {}}, {"path": "Report/PersonnelSection/PersonnelNotes", "tag": "PersonnelNotes", "text": "Test notes", "attributes": {}}, {"path": "Report/PersonnelSection/EmployeeChanges", "tag": "EmployeeChanges", "text": "Frequently", "attributes": {}}, {"path": "Report/PaymentsSection/PaymentHistory", "tag": "PaymentHistory", "text": "PaymentsImport", "attributes": {}}, {"path": "Report/PaymentsSection/QualificationNote", "tag": "QualificationNote", "text": "sadsad<PERSON>d adasdasdsadasdasd23131312312^^^^^^^^^^^^^^^^^^&&&&&&&&&&&&&&&&&&&&", "attributes": {}}, {"path": "Report/PaymentsSection/CreditOpinion", "tag": "CreditOpinion", "text": "Small", "attributes": {}}, {"path": "Report/PaymentsSection/CreditOpinionNotes", "tag": "CreditOpinionNotes", "text": "&&&&&&&&&&&&&&&&&&&&&&&&&&&&&", "attributes": {}}, {"path": "Report/PaymentsSection/MaxCreditCurrency", "tag": "MaxCreditCurrency", "text": "INR", "attributes": {}}, {"path": "Report/PaymentsSection/MaxCredit", "tag": "MaxCredit", "text": "50", "attributes": {}}, {"path": "Report/PaymentsSection/OpinionOnMaxCredit", "tag": "OpinionOnMaxCredit", "text": "NewCompany", "attributes": {}}, {"path": "Report/PaymentsSection/AdditionalNoteMaxCredit", "tag": "AdditionalNoteMaxCredit", "text": "qewwwwww423333333333))))))))))))))", "attributes": {}}, {"path": "Report/PaymentsSection/TradeRiskAssessment", "tag": "TradeRiskAssessment", "text": "No Classification", "attributes": {}}, {"path": "Report/PaymentsSection/CreditFigureCurrency", "tag": "CreditFigureCurrency", "text": "INR", "attributes": {}}, {"path": "Report/PaymentsSection/CreditFigure", "tag": "CreditFigure", "text": "5554", "attributes": {}}, {"path": "Report/PaymentsSection/AdditionalNoteCreditFigure", "tag": "AdditionalNoteCreditFigure", "text": "rwerwrwerwerwer", "attributes": {}}, {"path": "Report/PublicRecordSection/SanctionsSearch/SanctionsSearchPerfomed", "tag": "SanctionsSearchPerfomed", "text": "true", "attributes": {}}, {"path": "Report/PublicRecordSection/SanctionsSearch/SanctionsFound", "tag": "SanctionsFound", "text": "true", "attributes": {}}, {"path": "Report/PublicRecordSection/SanctionsSearch/SanctionComments", "tag": "SanctionComments", "text": "Sanction test comment", "attributes": {}}, {"path": "Report/PublicRecordSection/SanctionsSearch/CompanySanctions/CompanySanctionDetail", "tag": "CompanySanctionDetail", "text": "test", "attributes": {}}, {"path": "Report/PublicRecordSection/SanctionsSearch/CountrySanctions/CountrySanctionDetail", "tag": "CountrySanctionDetail", "text": "asdfsadsad", "attributes": {}}, {"path": "Report/PublicRecordSection/JudgementsSearch/JudgementsSearchPerfomed", "tag": "JudgementsSearchPerfomed", "text": "false", "attributes": {}}, {"path": "Report/PublicRecordSection/JudgementsSearch/JudgementsFound", "tag": "JudgementsFound", "text": "false", "attributes": {}}, {"path": "Report/PublicRecordSection/JudgementsSearch/JudgementComments", "tag": "JudgementComments", "text": "zxffsdfsdfsdfsdfsdfsdfsdfs                                                                                                                                                      sfdsfsdfsdfsdfsdfs                    safddsfsdf             asfdasd", "attributes": {}}, {"path": "Report/PublicRecordSection/PublicRecordInfo/OtherPublicRecordInformation", "tag": "OtherPublicRecordInformation", "text": "dfsdfsdf svdfsdfsdfdsfsdfds     ***************asdasdas asdasdas                                  fsffsdfsdf", "attributes": {}}, {"path": "Report/ChangesSection/Change/ChangeHeading", "tag": "ChangeHeading", "text": "SFSDFDS", "attributes": {}}, {"path": "Report/ChangesSection/Change/ChangeDate", "tag": "ChangeDate", "text": "01-May-2025", "attributes": {}}, {"path": "Report/ChangesSection/Change/ChangeClassification", "tag": "ChangeClassification", "text": "Mergers and Acquisitions", "attributes": {}}, {"path": "Report/ChangesSection/Change/ChangeDescription", "tag": "ChangeDescription", "text": "ASFDSF SFDSFSDF", "attributes": {}}, {"path": "Report/ChangesSection/Change/ChangeProvenances/ChangeProvenance/Provenance/ProvenanceID", "tag": "ProvenanceID", "text": "1", "attributes": {}}, {"path": "Report/ChangesSection/Change/ChangeProvenances/ChangeProvenance/Provenance/ProvenanceName", "tag": "ProvenanceName", "text": "Official", "attributes": {}}, {"path": "Report/ChangesSection/Change/ChangeProvenances/ChangeProvenance/Provenance/ProvenanceID", "tag": "ProvenanceID", "text": "4", "attributes": {}}, {"path": "Report/ChangesSection/Change/ChangeProvenances/ChangeProvenance/Provenance/ProvenanceName", "tag": "ProvenanceName", "text": "ICP", "attributes": {}}, {"path": "Report/ChangesSection/Change/ChangeProvenances/ChangeProvenance/Provenance/ProvenanceID", "tag": "ProvenanceID", "text": "5", "attributes": {}}, {"path": "Report/ChangesSection/Change/ChangeProvenances/ChangeProvenance/Provenance/ProvenanceName", "tag": "ProvenanceName", "text": "UnOfficial", "attributes": {}}, {"path": "Report/ChangesSection/Change/ChangeProvenances/ChangeProvenance/Provenance/ProvenanceID", "tag": "ProvenanceID", "text": "3", "attributes": {}}, {"path": "Report/ChangesSection/Change/ChangeProvenances/ChangeProvenance/Provenance/ProvenanceName", "tag": "ProvenanceName", "text": "Research", "attributes": {}}, {"path": "Report/ChangesSection/Change/ChangeProvenances/ChangeProvenance/Provenance/ProvenanceID", "tag": "ProvenanceID", "text": "2", "attributes": {}}, {"path": "Report/ChangesSection/Change/ChangeProvenances/ChangeProvenance/Provenance/ProvenanceName", "tag": "ProvenanceName", "text": "Subject", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/Name", "tag": "Name", "text": "AAA", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/Formerly", "tag": "Formerly", "text": "AAA", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/CareOf", "tag": "CareOf", "text": "AA", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/Building", "tag": "Building", "text": "AA", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/Street", "tag": "Street", "text": "AA**", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "IN", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/ICPCountryCode", "tag": "ICPCountryCode", "text": "IND", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/ICPCountryName", "tag": "ICPCountryName", "text": "India", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/InternationalDialingCode", "tag": "InternationalDialingCode", "text": "91", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/Number", "tag": "Number", "text": "SDFSDFDSF", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/Extension", "tag": "Extension", "text": "SDFSDFDSF", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/Type", "tag": "Type", "text": "Main", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/AccountNumber", "tag": "AccountNumber", "text": "DASDASDASD", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "IN", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/ICPCountryCode", "tag": "ICPCountryCode", "text": "IND", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/ICPCountryName", "tag": "ICPCountryName", "text": "India", "attributes": {}}, {"path": "Report/TradeReferencesSection/TradeReference/InternationalDialingCode", "tag": "InternationalDialingCode", "text": "91", "attributes": {}}, {"path": "Report/BankersSection/Bank/PrincipalBanker", "tag": "PrincipalBanker", "text": "true", "attributes": {}}, {"path": "Report/BankersSection/Bank/Name", "tag": "Name", "text": "&&&", "attributes": {}}, {"path": "Report/BankersSection/Bank/AccountTypes/Type", "tag": "Type", "text": "Commercial", "attributes": {}}, {"path": "Report/BankersSection/Bank/Formerly", "tag": "Formerly", "text": "*&*", "attributes": {}}, {"path": "Report/BankersSection/Bank/BranchName", "tag": "BranchName", "text": "((((", "attributes": {}}, {"path": "Report/BankersSection/Bank/Building", "tag": "Building", "text": "dfsfsdfsd", "attributes": {}}, {"path": "Report/BankersSection/Bank/Town", "tag": "Town", "text": "UUU", "attributes": {}}, {"path": "Report/BankersSection/Bank/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "IN", "attributes": {}}, {"path": "Report/BankersSection/Bank/ICPCountryCode", "tag": "ICPCountryCode", "text": "IND", "attributes": {}}, {"path": "Report/BankersSection/Bank/ICPCountryName", "tag": "ICPCountryName", "text": "India", "attributes": {}}, {"path": "Report/BankersSection/Bank/InternationalDialingCode", "tag": "InternationalDialingCode", "text": "91", "attributes": {}}, {"path": "Report/BankersSection/Bank/Number", "tag": "Number", "text": "sdfsdfdsf**", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/Active", "tag": "Active", "text": "true", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/ServiceTypes/Type", "tag": "Type", "text": "Audit", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/Building", "tag": "Building", "text": "No. 7, Bishops Court", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/Street", "tag": "Street", "text": "Corner of Delamore and Warrior Roads", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/POBox", "tag": "POBox", "text": "10546, <PERSON><PERSON> 3605", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/Town", "tag": "Town", "text": "Hillcrest", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/ProvinceType", "tag": "ProvinceType", "text": "Province", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/Province", "tag": "Province", "text": "KwaZulu-Natal", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/PostCode", "tag": "PostCode", "text": "3610", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "ZA", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/ICPCountryCode", "tag": "ICPCountryCode", "text": "SOU", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/ICPCountryName", "tag": "ICPCountryName", "text": "South Africa", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/InternationalDialingCode", "tag": "InternationalDialingCode", "text": "27", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/Number", "tag": "Number", "text": "31 765 3111", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/Type", "tag": "Type", "text": "Main", "attributes": {}}, {"path": "Report/AccountantsSection/Accountant/Email", "tag": "Email", "text": "micha<PERSON>@wildnerandcompany.co.za", "attributes": {}}, {"path": "Report/CompanySecretarySection/Name", "tag": "Name", "text": "***", "attributes": {}}, {"path": "Report/CompanySecretarySection/CareOf", "tag": "CareOf", "text": "))", "attributes": {}}, {"path": "Report/CompanySecretarySection/POBox", "tag": "POBox", "text": "&&", "attributes": {}}, {"path": "Report/CompanySecretarySection/Town", "tag": "Town", "text": "$$", "attributes": {}}, {"path": "Report/CompanySecretarySection/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "IN", "attributes": {}}, {"path": "Report/CompanySecretarySection/ICPCountryCode", "tag": "ICPCountryCode", "text": "IND", "attributes": {}}, {"path": "Report/CompanySecretarySection/ICPCountryName", "tag": "ICPCountryName", "text": "India", "attributes": {}}, {"path": "Report/CompanySecretarySection/InternationalDialingCode", "tag": "InternationalDialingCode", "text": "91", "attributes": {}}, {"path": "Report/LawyersSection/Lawyer/Active", "tag": "Active", "text": "true", "attributes": {}}, {"path": "Report/LawyersSection/Lawyer/Name", "tag": "Name", "text": "@@@", "attributes": {}}, {"path": "Report/LawyersSection/Lawyer/ServiceTypes/Type", "tag": "Type", "text": "All Legal Services", "attributes": {}}, {"path": "Report/LawyersSection/Lawyer/Formerly", "tag": "Formerly", "text": "!!", "attributes": {}}, {"path": "Report/LawyersSection/Lawyer/Town", "tag": "Town", "text": "&&", "attributes": {}}, {"path": "Report/LawyersSection/Lawyer/ISO2CountryCode", "tag": "ISO2CountryCode", "text": "IN", "attributes": {}}, {"path": "Report/LawyersSection/Lawyer/ICPCountryCode", "tag": "ICPCountryCode", "text": "IND", "attributes": {}}, {"path": "Report/LawyersSection/Lawyer/ICPCountryName", "tag": "ICPCountryName", "text": "India", "attributes": {}}, {"path": "Report/LawyersSection/Lawyer/InternationalDialingCode", "tag": "InternationalDialingCode", "text": "91", "attributes": {}}, {"path": "Report/LawyersSection/Lawyer/Number", "tag": "Number", "text": "AAAAAAAAAAAA", "attributes": {}}, {"path": "Report/LawyersSection/Lawyer/Extension", "tag": "Extension", "text": "BB", "attributes": {}}, {"path": "Report/LawyersSection/Lawyer/Type", "tag": "Type", "text": "Main", "attributes": {}}, {"path": "Report/LawyersSection/Lawyer/Position", "tag": "Position", "text": "Lawyer", "attributes": {}}, {"path": "Report/LegalStatusSection/CompanyStatus", "tag": "CompanyStatus", "text": "Active", "attributes": {}}, {"path": "Report/LegalStatusSection/Capital/NotGiven", "tag": "NotGiven", "text": "false", "attributes": {}}, {"path": "Report/LegalStatusSection/ICPLegalGroup", "tag": "ICPLegalGroup", "text": "Unlimited Partnership", "attributes": {}}, {"path": "Report/LegalStatusSection/LegalStatusCode", "tag": "LegalStatusCode", "text": "3000", "attributes": {}}, {"path": "Report/LegalStatusSection/LegalStatus", "tag": "LegalStatus", "text": "General Partnership", "attributes": {}}, {"path": "Report/SpecialNotesSection/TransferToPrincipal", "tag": "TransferToPrincipal", "text": "false", "attributes": {}}, {"path": "Report/WaiverSection/Waiver", "tag": "Waiver", "text": "All information within this report is provided entirely on an 'as is' basis and is used by the recipient at its sole risk. ICP shall use reasonable skill and care in providing information contained within this report and, specifically, in collecting and collating the information from reliable sources and will have no legal responsibility for any error or omission contained within this report or subsequent change to the information. It does not give any warranty or guarantee to information provided or consequences of using the information within this report for decision making purposes.\n\nInformation is to be stored and used in accordance with data protection regulation and will not be supplied, published, displayed, re-sold, copied or made known to the subject without prior agreement from ICP.", "attributes": {}}], "root_element": "Report", "namespace": {}, "processing_timestamp": "2025-07-11T16:54:50.989452"}